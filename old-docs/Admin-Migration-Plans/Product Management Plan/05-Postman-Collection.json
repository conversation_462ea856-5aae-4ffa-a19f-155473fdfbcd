{"info": {"name": "Product Management Staff API", "description": "Complete API collection for staff product management operations", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{staff_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api/staff", "type": "string"}, {"key": "staff_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Login Staff User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login/", "host": ["{{base_url}}"], "path": ["auth", "login", ""]}}}]}, {"name": "Product Management", "item": [{"name": "List Products", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products/?page=1&page_size=20&search=laptop&is_active=true", "host": ["{{base_url}}"], "path": ["products", ""], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}, {"key": "search", "value": "laptop"}, {"key": "is_active", "value": "true"}]}}}, {"name": "Get Product Details", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products/1/", "host": ["{{base_url}}"], "path": ["products", "1", ""]}}}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"New Gaming Laptop\",\n  \"slug\": \"new-gaming-laptop\",\n  \"brand\": 1,\n  \"category\": 1,\n  \"product_type\": 1,\n  \"description\": \"High-performance gaming laptop\",\n  \"is_active\": true,\n  \"is_digital\": false\n}"}, "url": {"raw": "{{base_url}}/products/", "host": ["{{base_url}}"], "path": ["products", ""]}}}, {"name": "Update Product", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Gaming Laptop\",\n  \"description\": \"Updated description\"\n}"}, "url": {"raw": "{{base_url}}/products/1/", "host": ["{{base_url}}"], "path": ["products", "1", ""]}}}, {"name": "Bulk Operations", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation_type\": \"activate\",\n  \"product_ids\": [1, 2, 3, 4, 5]\n}"}, "url": {"raw": "{{base_url}}/products/bulk_operations/", "host": ["{{base_url}}"], "path": ["products", "bulk_operations", ""]}}}, {"name": "Create Product with Variants", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product\": {\n    \"title\": \"Gaming Laptop Pro\",\n    \"slug\": \"gaming-laptop-pro\",\n    \"brand\": 1,\n    \"category\": 1,\n    \"product_type\": 1,\n    \"description\": \"Professional gaming laptop\"\n  },\n  \"variants\": [\n    {\n      \"price\": \"1299.99\",\n      \"sku\": \"LAPTOP-PRO-16GB\",\n      \"stock_qty\": 10,\n      \"condition\": \"New\"\n    },\n    {\n      \"price\": \"1599.99\",\n      \"sku\": \"LAPTOP-PRO-32GB\",\n      \"stock_qty\": 5,\n      \"condition\": \"New\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/products/create_with_variants/", "host": ["{{base_url}}"], "path": ["products", "create_with_variants", ""]}}}, {"name": "Product Analytics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products/analytics/", "host": ["{{base_url}}"], "path": ["products", "analytics", ""]}}}]}, {"name": "Category Management", "item": [{"name": "List Categories", "request": {"method": "GET", "url": {"raw": "{{base_url}}/categories/", "host": ["{{base_url}}"], "path": ["categories", ""]}}}, {"name": "Category Tree", "request": {"method": "GET", "url": {"raw": "{{base_url}}/categories/tree/", "host": ["{{base_url}}"], "path": ["categories", "tree", ""]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"New Category\",\n  \"slug\": \"new-category\",\n  \"is_active\": true,\n  \"parent\": null\n}"}, "url": {"raw": "{{base_url}}/categories/", "host": ["{{base_url}}"], "path": ["categories", ""]}}}, {"name": "Move Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"new_parent_id\": 2\n}"}, "url": {"raw": "{{base_url}}/categories/1/move/", "host": ["{{base_url}}"], "path": ["categories", "1", "move", ""]}}}]}, {"name": "Attribute Management", "item": [{"name": "List Attributes", "request": {"method": "GET", "url": {"raw": "{{base_url}}/attributes/", "host": ["{{base_url}}"], "path": ["attributes", ""]}}}, {"name": "List Attribute Values", "request": {"method": "GET", "url": {"raw": "{{base_url}}/attribute-values/?attribute=1", "host": ["{{base_url}}"], "path": ["attribute-values", ""], "query": [{"key": "attribute", "value": "1"}]}}}, {"name": "Bulk Create Attribute Values", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"attribute_id\": 1,\n  \"values\": [\"<PERSON>\", \"<PERSON>\", \"Green\", \"Yellow\"]\n}"}, "url": {"raw": "{{base_url}}/attribute-values/bulk_create/", "host": ["{{base_url}}"], "path": ["attribute-values", "bulk_create", ""]}}}]}, {"name": "Association Management", "item": [{"name": "Product Type Attributes", "request": {"method": "GET", "url": {"raw": "{{base_url}}/associations/product_type_attributes/?product_type_id=1", "host": ["{{base_url}}"], "path": ["associations", "product_type_attributes", ""], "query": [{"key": "product_type_id", "value": "1"}]}}}, {"name": "Save Association", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_type\": 1,\n  \"attribute\": 1,\n  \"is_filterable\": true,\n  \"is_option_selector\": false\n}"}, "url": {"raw": "{{base_url}}/associations/save_association/", "host": ["{{base_url}}"], "path": ["associations", "save_association", ""]}}}, {"name": "Associate Attributes Bulk", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_type_id\": 1,\n  \"attributes\": [\n    {\n      \"attribute_id\": 1,\n      \"is_filterable\": true,\n      \"is_option_selector\": false\n    },\n    {\n      \"attribute_id\": 2,\n      \"is_filterable\": false,\n      \"is_option_selector\": true\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/product-types/1/associate_attributes/", "host": ["{{base_url}}"], "path": ["product-types", "1", "associate_attributes", ""]}}}]}, {"name": "Audit & Operations", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/audit/?product_id=1", "host": ["{{base_url}}"], "path": ["audit", ""], "query": [{"key": "product_id", "value": "1"}]}}}, {"name": "Bulk Operations Status", "request": {"method": "GET", "url": {"raw": "{{base_url}}/bulk-operations/", "host": ["{{base_url}}"], "path": ["bulk-operations", ""]}}}]}]}