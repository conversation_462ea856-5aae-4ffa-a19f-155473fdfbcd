### Order Creation, Validation and Updating Workflow

1. The user creates an order using the frontend application. This sends a POST request to the backend,
   specifically to the OrderViewSet's create method.
2. The backend validates the request data and creates an order in the database with payment_status set to 'Pending'.
3. The backend responds with the created order's details, including the order ID.
   Payment Initialization:
4. The user proceeds to make a payment. The frontend sends a POST request to the StripeCheckoutView endpoint with the
   order
   details (amount, email, name, order_id).
5. The backend validates the request data and creates a Stripe customer and a PaymentIntent with the provided amount and
   **order_id** included in the _metadata_.
6. The backend responds with the clientSecret from the PaymentIntent, which is used by the frontend to complete the
   payment process with <PERSON><PERSON>.

   #### Payment Completion:

7. The user completes the payment on the frontend using Strip<PERSON>'s provided clientSecret.
8. <PERSON><PERSON> processes the payment and, upon successful completion, triggers a **payment_intent.succeeded** webhook event.

   #### Webhook Handling:
9. <PERSON><PERSON> sends the payment_intent.succeeded event to the StripeWebhookView endpoint configured in the backend.
10. The webhook handler verifies the event's signature to ensure it's from Stripe.
11. The handler extracts the **order_id** from the event's _metadata_ and updates the corresponding
    order's **payment_status** to **Paid**' in the database.

