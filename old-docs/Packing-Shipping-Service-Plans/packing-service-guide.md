# 📆 Step-by-Step Guide: Adding Packing Cost & Box Selection in Django

This guide helps you integrate rule-based packaging logic and packing cost tracking into your existing Django e-commerce
project.

---

## ✅ 1. Model Updates

### 🔢 Product Model

**Update your **``** model** in `product/models.py`:

```python
from decimal import Decimal


class Product(models.Model):
    ...
    can_ship_in_mailer = models.BooleanField(default=False, help_text="Can be packed in a padded envelope")
    packing_cost = models.DecimalField(
        max_digits=5, decimal_places=2, default=Decimal('0.00'),
        help_text="Labor/packing cost for this item")
```

### 🛋️ Box Model

**Update your **``** model** (create a new app like `shipping` if it doesn't exist yet):

```python
class Box(models.Model):
    title = models.CharField(max_length=100)
    inner_length = models.FloatField()
    inner_width = models.FloatField()
    inner_height = models.FloatField()
    max_weight = models.FloatField()
    box_price = models.DecimalField(max_digits=6, decimal_places=2, default=Decimal('0.00'))
    is_mailer = models.BooleanField(default=False, help_text="Is this a mailer (padded envelope)?")
```

### 📋 Order Model

**Update your **``** model** in `order/models.py`:

```python
from decimal import Decimal


class Order(models.Model):
    ...
    packing_fee = models.DecimalField(
        max_digits=6, decimal_places=2, default=Decimal('0.00')
    )
```

---

## 🛠️ 2. Create PackingService

**Create **``**:**

```python
from decimal import Decimal
from apps.shipping.models import Box  # adjust if your app name is different


class PackingService:
    @staticmethod
    def determine_packaging(order_item):
        product = order_item.product

        if product.can_ship_in_mailer:
            mailer = Box.objects.filter(is_mailer=True).order_by('box_price').first()
            return mailer, product.packing_cost

        # Assume rule-based selection for boxes
        box = PackingService.select_smallest_fitting_box(product, order_item.quantity)
        box_cost = box.box_price if box else Decimal('0.00')
        return box, product.packing_cost + box_cost

    @staticmethod
    def select_smallest_fitting_box(product, quantity):
        total_volume = product_volume(product) * quantity
        boxes = Box.objects.filter(is_mailer=False).order_by('inner_length', 'inner_width', 'inner_height')
        for box in boxes:
            box_volume = box.inner_length * box.inner_width * box.inner_height
            if box_volume >= total_volume:
                return box
        return None

    @staticmethod
    def calculate_total_packing_fee(order_items):
        total_fee = Decimal('0.00')
        for item in order_items:
            box, fee = PackingService.determine_packaging(item)
            total_fee += fee
        return total_fee


def product_volume(product):
    # Replace with actual volume logic (via product attributes if available)
    length = getattr(product, 'length', 10)
    width = getattr(product, 'width', 10)
    height = getattr(product, 'height', 2)
    return length * width * height
```

---

## 📃 3. Update Order Creation Logic

In your **checkout view or service** that converts cart to order:

```python
from services.packing_service import PackingService

# After creating Order and OrderItems:
order.packing_fee = PackingService.calculate_total_packing_fee(order.ordered_items.all())
order.total = order.subtotal + order.shipping_cost + order.packing_fee
order.save()
```

---

## 💼 4. Admin & Migrations

1. Run migrations:

```bash
python manage.py makemigrations
python manage.py migrate
```

2. Add fields to Django Admin:

- `ProductAdmin`: show `can_ship_in_mailer`, `packing_cost`
- `BoxAdmin`: show `box_price`, `is_mailer`
- `OrderAdmin`: show `packing_fee`

---

## 📊 5. Optional Enhancements

- Add a `ProductDimension` model for more accurate volume calculation
- Display box/packing breakdown in order summary page
- Support for fragile or special handling flags

---

Let me know if you'd like:

- A `Cart` version of this logic
- Unit tests
- HTML templates showing packing breakdown

