# Step-by-Step Guideline for Creating Signals and Handlers

### Introduction

This guideline outlines the general steps to create and use signals and handlers in Django.
**Signals** allow decoupled parts of the application to communicate by notifying when certain
actions occur (such as a model being saved or an order being created). This ensures that
related actions can be performed without tightly coupling different components of the system.

### Built-in Signals

- Django provides several built-in signals like `post_save`, `pre_save`, `post_delete`, and more.
  These signals notify when certain events occur in models.
- Use built-in signals when you want to trigger actions based on model events.
- This is a signal to create a customer profile when a new user is registered.

```python
from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver
from apps.customers.models import Customer


@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def create_customer_for_new_user(sender, **kwargs):
    if kwargs['created']:
        Customer.objects.create(user=kwargs['instance'])
```

## Custom Signal Workflow Example

This section outlines the general **workflow** of creating and using a signal in
a Django project. Follow these steps to implement a signal from scratch, ensuring
it is triggered correctly and its handler is executed.

### Step 1: Identify the Event for the Signal

- Determine what event in the application should trigger the signal.
- Examples: A **user registers** in the system, an **order is placed**, a **model is saved**, or a **payment is
  completed**.
- Decide whether you can use a **built-in signal** (like `post_save`) or if you need to create a **custom signal** for
  the event.

### Step 2: Define the Signal

- If you are using a **custom signal**, define the signal in the appropriate app.
- Typically, a custom signal is defined when there’s no suitable built-in signal for the event.
- The signal should be descriptive and provide relevant arguments needed for its handler.
- create a file named `signals.py` in the app directory.

_It is recommended to create a folder named `signals` in the app directory. Then,
create __init__.py, signals.py, and handlers.py files in the same folder._

```python
from django.dispatch import Signal

# Define a custom signal for low stock alerts
low_stock_alert = Signal(providing_args=["product_variant"])
```

### Step 3: Create the Signal Handler

- Define a **signal handler**, which is a function that will execute when the signal is triggered.
- The handler should perform the necessary action in response to the event, such as sending an email, creating a related
  model, or updating data.
- Keep the handler logic simple and focused on reacting to the signal.
- Create a file named `handlers.py` in the app directory.

```python
from django.conf import settings
from django.core.mail import send_mail
from django.dispatch import receiver
from .signals import low_stock_alert  # Import the custom signal


@receiver(low_stock_alert)
def send_low_stock_alert(sender, product_variant, **kwargs):
    # Sends an email alert when a product's stock is low.
    subject = f'Low Stock Alert: {product_variant.product.title} - {product_variant.price_label}'
    message = (
        f'The stock quantity for {product_variant.product.title} '
        f'({product_variant.price_label}) has dropped below {product_variant.stock_qty + 1}. '
        f'Current stock: {product_variant.stock_qty}'
    )
    from_email = settings.DEFAULT_FROM_EMAIL
    recipient_list = [settings.STORE_OWNER_EMAIL]
    send_mail(subject, message, from_email, recipient_list)
```

### Step 4: Connect the Signal and Handler

- Signals need to be connected to their handlers so that when the signal is triggered,
  the handler executes.
- In Django, you typically connect signals to handlers by using the **@receiver decorator**
  or manually connecting them.
- Ensure the connection is registered by importing the handlers in the app’s `apps.py` file
  during the app’s startup.
- In the `apps.py` file, import the signal handlers and connect them to the signal.

```python
from django.apps import AppConfig


class ProductsConfig(AppConfig):
    name = 'apps.products'

    def ready(self):
        # Import signal handlers, so they are registered when the app starts
        import apps.products.handlers
```

### Step 5: Trigger the Signal

- Once the signal and handler are connected, trigger the signal when the relevant event occurs.
- This can happen in models (e.g., after saving an object), views (e.g., after processing a form),
  or serializers.
- Use the `.send()` method to trigger the signal, passing any necessary arguments to the signal's handler.
- In other words, this is the method that called the signal defined in signals.py file.

```python
from apps.products.signals import low_stock_alert  # Import the custom signal


class CreateOrderSerializer(serializers.Serializer):
    # (Other fields...)

    def save(self, **kwargs):
        # (Other code...)

        # Trigger the low stock alert signal if stock is low
        if product_variant.stock_qty < 5:
            low_stock_alert.send(sender=self.__class__, product_variant=product_variant)

        # Continue with the order creation process...
```

### Step 6: Test the Signal and Handler

- After implementing the signal, thoroughly **test** to ensure that the signal is triggered correctly and the handler
  performs the expected actions.
- Test edge cases and potential failures (e.g., missing required arguments or handler errors).
- If multiple handlers are connected to a signal, ensure they all behave correctly when the
  signal is triggered.

### Step 7: Maintain and Optimize

- As the project evolves, keep your signal logic up-to-date.
- Regularly review your signal handlers to ensure they remain efficient and follow best practices (e.g., keeping logic
  simple).
- Avoid chaining signals, which can complicate debugging and lead to unintended consequences.

### Best Practices for Signals

- **Modularity**: Store signals and their handlers in separate files
  (e.g., `signals.py` and `handlers.py`).
  This keeps your code organized and helps with readability.
- **Minimize Logic in Handlers**: Avoid putting too much logic in signal handlers.
  They should be simple and delegate complex operations to models or services.
- **Use `send_robust()`**: When triggering signals, consider using `send_robust()` to ensure that the
  signal still functions even if one of the handlers raises an error.
- **Testing**: Always test your signals to ensure they behave as expected. Use mocks or stubs
  to test how signals are triggered and handled.

### When to Use Signals

- **Decoupling**: Use signals when you need to decouple different parts of your system. For example,
  updating a profile or sending an email after a user is created.
- **Cross-App Communication**: Signals are useful when actions in one app affect another app. For example,
  creating an order in one app might trigger a stock check in another app.
- **Event-Driven Actions**: Use signals to trigger actions when specific events happen in your application,
  such as sending notifications, logging activity, or updating related models.

### Common Use Cases for Signals

1. **User Registration**: When a user registers, trigger a signal to create a profile, send a welcome
   email, or log activity.
2. **Order Creation**: When an order is created, trigger a signal to update stock levels or notify
   the customer.
3. **Model Updates**: Use signals like `pre_save` or `post_save` to perform actions when models
   are saved (e.g., updating related fields, validating data).