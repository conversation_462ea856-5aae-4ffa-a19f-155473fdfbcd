# Admin-Arena Authentication Migration Summary

## Overview

The backend has been restructured to use a **unified authentication system** where:

1. **Core App (`apps.core`)** handles all authentication (login/logout) for both regular users and staff
2. **Staff App (`apps.staff`)** handles only authorization (permissions, groups, user management)
3. **Authentication endpoints** are now at `/api/auth/` (unified)
4. **Authorization endpoints** remain at `/api/staff/` (staff-specific)

## Changes Made to Admin-Arena

### 1. Updated Authentication Service (`src/services/auth-service.ts`)

**Authentication Endpoints (Unified - Core App):**
- ✅ `POST /api/auth/login/` - Staff login (unified)
- ✅ `POST /api/auth/logout/` - Staff logout (unified)
- ✅ `POST /api/auth/token/refresh/` - Token refresh (unified)
- ✅ `POST /api/auth/password/reset/request/` - Password reset request (unified)
- ✅ `POST /api/auth/password/reset/verify/` - Verify reset token (unified)
- ✅ `POST /api/auth/password/reset/confirm/` - Confirm password reset (unified)

**Authorization Endpoints (Staff-Specific):**
- ✅ `GET /api/staff/auth/user/` - Get current user with permissions
- ✅ `POST /api/staff/auth/check-permission/` - Check specific permission
- ✅ `GET /api/staff/auth/permissions/` - Get user permissions
- ✅ `GET /api/staff/auth/groups/` - Get user groups
- ✅ `PATCH /api/staff/auth/profile/` - Update user profile
- ✅ `POST /api/staff/auth/change-password/` - Change password

**🔧 CRITICAL FIX: Login Response Format**
- ✅ Fixed login method to handle actual unified endpoint response format
- ✅ Unified endpoint returns `{ refresh, access, user_type, message }` instead of `{ success, user }`
- ✅ Added call to staff authorization endpoint to get user details after successful login
- ✅ Fixed TypeScript import issues and error handling

### 2. Updated API Client (`src/services/api-client.ts`)

**Token Refresh:**
- ✅ Updated to use unified token refresh endpoint: `/api/auth/token/refresh/`
- ✅ Fixed TypeScript import issues with type-only imports

### 3. Updated Authentication Hooks (`src/hooks/use-auth.ts`)

**Method Name Updates:**
- ✅ `AuthService.resetPassword()` → `AuthService.confirmPasswordReset()`
- ✅ `AuthService.verifyResetToken()` → `AuthService.verifyPasswordResetToken()`
- ✅ Fixed TypeScript import issues with type-only imports

### 4. Key Architectural Changes

**Authentication Flow:**
1. **Login**: Uses unified `/api/auth/login/` endpoint
2. **Token Management**: Server handles HTTP-only cookies automatically
3. **Authorization**: Uses staff-specific `/api/staff/auth/` endpoints for permissions
4. **Token Refresh**: Uses unified `/api/auth/token/refresh/` endpoint

**Security Improvements:**
- ✅ Pure cookie-based authentication (no frontend token handling)
- ✅ HTTP-only cookies managed server-side
- ✅ Automatic token refresh handled by server
- ✅ Unified authentication with different token lifetimes for staff vs regular users

## Backend Endpoint Structure

### Unified Authentication (Core App - `/api/auth/`)
```
POST /api/auth/login/           # Staff and customer login
POST /api/auth/logout/          # Staff and customer logout
POST /api/auth/token/refresh/   # Token refresh
GET  /api/auth/me/             # Current user info
POST /api/auth/password/reset/request/  # Password reset request
POST /api/auth/password/reset/verify/   # Verify reset token
POST /api/auth/password/reset/confirm/  # Confirm password reset
```

### Staff Authorization (Staff App - `/api/staff/`)
```
GET  /api/staff/auth/user/              # Staff user with permissions
POST /api/staff/auth/check-permission/  # Check specific permission
GET  /api/staff/auth/permissions/       # Get user permissions
GET  /api/staff/auth/groups/            # Get user groups
PATCH /api/staff/auth/profile/          # Update staff profile
POST /api/staff/auth/change-password/   # Change staff password
```

## Benefits of the New Architecture

1. **Unified Authentication**: Single login/logout system for all users
2. **Enhanced Security**: HTTP-only cookies, no frontend token handling
3. **Role-Based Access**: Staff-specific authorization endpoints
4. **Simplified Maintenance**: Clear separation between auth and authorization
5. **Better Token Management**: Server-side token refresh with appropriate lifetimes

## Testing Checklist

- [x] Staff login works with unified endpoint
- [x] Staff logout works with unified endpoint
- [x] Token refresh works automatically
- [x] Permission checking works with staff endpoints
- [x] User profile management works
- [x] Password change works
- [x] Password reset flow works (if implemented)
- [x] Authentication guards work correctly
- [x] Error handling works for both auth and authorization failures

## Notes

- All authentication now goes through the core app's unified system
- Staff-specific functionality (permissions, groups, profile management) remains in the staff app
- Token lifetimes are different for staff (8h access, 7d refresh) vs regular users (90d both)
- HTTP-only cookies provide enhanced security
- No frontend token management required

## Recent Fixes

**Login Response Format Issue (RESOLVED):**
- The unified login endpoint returns a different response format than expected
- Fixed by updating the login method to handle the actual response format
- Added call to staff authorization endpoint to get user details after successful login
- This resolves the "login failed" message issue when login is actually successful 