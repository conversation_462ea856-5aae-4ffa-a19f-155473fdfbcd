# Product Variant Selection Technical Documentation

## Overview

This document explains how the product variant selection functionality works in the Picky E-commerce application. When a user views a product with multiple variants (e.g., different sizes, colors, materials), the application dynamically displays available attribute combinations and updates the UI as the user makes selections.

## Data Structure

### Product Data Model

The product data received from the server includes:

1. **Product Information**: Basic details like title, description, and category.
2. **Product Variants**: Different versions of the product with unique combinations of attributes.
3. **Option Selectors**: Available attribute options that can be selected.

### Key Data Structures

- **Product Variant**: Contains specific attribute combinations, price, stock quantity, images, and an `order` field for display ordering.
- **Attribute Value**: Represents a specific value for an attribute (e.g., "Red" for the "Color" attribute).
- **Option Selector**: Groups attribute values by their attribute type (e.g., all available colors).

### Order-Based Display

All product variants include an `order` field that determines their display sequence:

- **Variant Ordering**: Product variants are sorted by their `order` field (ascending) to ensure consistent display.
- **Primary Attribute Ordering**: When grouping variants by primary attribute (e.g., Size), the groups are ordered based on the lowest `order` value within each group.
- **Selection Priority**: When multiple variants match the same attribute combination, the variant with the lowest `order` value is selected.
- **Availability Ordering**: Available attribute values are presented in the order they appear in the sorted variants.

## Component Architecture

The product variant selection functionality is implemented through several interconnected components:

1. **ProductDetails**: The main container component that loads product data.
2. **ProductDetailsInfo**: Handles the display and selection of product variants.
3. **SelectableAttributeValues**: Renders attribute options when no option selectors are available.

## Selection Flow

### Initial Load

1. When a product page loads, the `ProductDetails` component fetches the product data.
2. The first available product variant is automatically selected.
3. The UI displays the primary attribute (e.g., Size) and secondary attributes (e.g., Color).

### Primary Attribute Selection

1. The primary attribute (defined by `price_label_attr_title` in the product data) is displayed first.
2. When a user selects a primary attribute value (e.g., Size "M"):
   - The corresponding variant is selected.
   - The `selectedAttributes` state is updated with the selected value.
   - Secondary attribute options (attribute values) are filtered to show only those available with the selected primary attribute.
   - The UI updates to highlight the selected value.
3. **Toggle Functionality**: If a user clicks on an already selected primary attribute value:
   - The selection is deselected and all attribute selections are reset.
   - The system reverts to the first variant (by order) with no specific attribute selections.
   - All secondary attribute options become available again.

### Secondary Attribute Selection

1. Secondary attributes (e.g., Color) are displayed with available options.
2. When a user selects a secondary attribute value (e.g., Color "Red"):
   - The `selectedAttributes` state is updated with the new selection.
   - The application finds a matching variant that has all the selected attribute values.
   - If a matching variant is found, it becomes the selected variant.
   - The UI updates to reflect the new selection and product details.
3. **Toggle Functionality**: If a user clicks on an already selected secondary attribute value:
   - Only that specific attribute selection is removed from `selectedAttributes`.
   - The system finds a variant that matches the remaining selected attributes.
   - Available options for other attributes are recalculated based on the remaining selections.
   - If no attributes remain selected, the system reverts to the first variant (by order).

### Cascading Availability Filtering

1. After a primary attribute is selected, secondary attribute values that aren't available with the selected primary value are disabled.
2. When a secondary attribute is selected, any remaining attribute options are further filtered based on both the primary and secondary selections.
3. This cascading filtering continues with each attribute selection, ensuring users can only select valid combinations.
4. For example, if a user selects Size "M" and Color "Green", the Material options might be filtered to only show "Silk" if that's the only available material for that specific combination.
5. The `isSecondaryValueAvailable` function determines which values can be selected based on all currently selected attributes.
6. Unavailable options are visually distinguished (grayed out) and cannot be selected.

## Step-by-Step Implementation

### 1. Loading Product Data

```typescript
// In ProductDetails.tsx
useEffect(() => {
  if (!isPending && !error && product) {
    // Always set the variant with the lowest order value (first in display order)
    if (product.product_variant && product.product_variant.length > 0) {
      // Sort variants by order and select the first one
      const sortedVariants = [...product.product_variant].sort((a, b) => a.order - b.order)
      setProductVariant(sortedVariants[0])
    }

    // Find the first available image across all variants
    const imageUrl = findFirstAvailableImage(product, import.meta.env.VITE_CLOUDINARY_URL)
    if (imageUrl) {
      setSelectedImage(imageUrl)
    }
  }
}, [isPending, error, product, setProductVariant])
```

### 2. Initializing Selected Attributes

```typescript
// In ProductDetailsInfo.tsx
useEffect(() => {
  if (selectedVariant && Object.keys(selectedAttributes).length === 0) {
    // Initialize selected attributes based on the selected variant
    const initialAttributes: Record<string, string> = {}
    selectedVariant.attribute_value.forEach(attr => {
      initialAttributes[attr.attribute.title] = attr.attribute_value
    })
    setSelectedAttributes(initialAttributes)
  }
}, [selectedVariant, selectedAttributes])
```

### 3. Handling Primary Attribute Selection with Toggle

```typescript
// In ProductDetailsInfo.tsx
const handlePrimarySelection = (value: string) => {
  // Check if this primary attribute value is already selected (toggle functionality)
  const isCurrentlySelected = selectedAttributes[primaryAttributeTitle] === value

  if (isCurrentlySelected) {
    // If already selected, deselect it (reset to initial state)
    const sortedVariants = [...(product?.product_variant || [])].sort((a, b) => a.order - b.order)
    if (sortedVariants.length > 0) {
      // Select the first variant (by order) and reset all selections
      handleVariantClick(sortedVariants[0])
      setSelectedAttributes({})
      setExtraData({})
    }
  } else {
    // If not selected, select it
    const variantsWithPrimaryValue = product?.product_variant?.filter(v => v.price_label === value) || []
    if (variantsWithPrimaryValue.length > 0) {
      // Sort by order and select the first one
      const sortedVariants = variantsWithPrimaryValue.sort((a, b) => a.order - b.order)
      const variant = sortedVariants[0]

      handleVariantClick(variant)

      // Reset all attribute selections and only keep the primary attribute
      const newAttributes: Record<string, string> = {
        [primaryAttributeTitle]: value
      }

      // Also reset the extra data in cart store
      const newExtraData: Record<string, string> = {
        [primaryAttributeTitle]: value
      }

      setSelectedAttributes(newAttributes)
      setExtraData(newExtraData)
    }
  }
}
```

### 4. Handling Attribute Selection with Cascading Filtering

```typescript
// In ProductDetailsInfo.tsx
const handleAttributeSelection = (attributeTitle: string, _valueId: number, valueText: string) => {
  // Create new attributes object with the new selection
  const newAttributes = {
    ...selectedAttributes,
    [attributeTitle]: valueText
  }
  setSelectedAttributes(newAttributes)

  // Update extra data in cart store
  const newExtraData = {
    ...cartStore.getState().cartItem.extra_data,
    [attributeTitle]: valueText
  }
  setExtraData(newExtraData)

  // Find a matching variant with the selected attributes
  const matchingVariant = product?.product_variant?.find(variant => {
    // Check if this variant matches all selected attributes
    return Object.entries(newAttributes).every(([attrTitle, attrValue]) => {
      return variant.attribute_value.some(attr =>
        attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
        attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
      )
    })
  })

  if (matchingVariant) {
    handleVariantClick(matchingVariant)

    // If we found a matching variant, we might need to update the UI
    // to reflect any cascading changes to other attribute options
    updateAvailableAttributeOptions(newAttributes)
  }
}

// Helper function to update available options for all attributes based on current selections
const updateAvailableAttributeOptions = (selectedAttrs: Record<string, string>) => {
  // For each attribute selector, determine which values are available
  // based on the current selections

  // This could involve re-rendering the attribute selectors with updated
  // availability information, or updating a state that tracks which
  // values are available for each attribute
}
```

### 5. Determining Available Attribute Values with Cascading Filtering

```typescript
// In ProductDetailsInfo.tsx
const isAttributeValueAvailable = (attributeTitle: string, valueText: string) => {
  if (!selectedVariant) return false

  // If no attributes are selected yet, all values are available
  if (Object.keys(selectedAttributes).length === 0) return true

  // Find variants that match ALL currently selected attributes
  const matchingVariants = product?.product_variant?.filter(variant => {
    // Check if this variant matches all currently selected attributes
    return Object.entries(selectedAttributes).every(([attrTitle, attrValue]) => {
      return variant.attribute_value.some(attr =>
        attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
        attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
      )
    })
  }) || []

  // Check if any of these matching variants have the attribute value we're checking
  return matchingVariants.some(variant =>
    variant.attribute_value.some(attr =>
      attr.attribute.title.toLowerCase() === attributeTitle.toLowerCase() &&
      attr.attribute_value.toLowerCase() === valueText.toLowerCase()
    )
  )
}
```

This improved function checks availability based on all currently selected attributes, not just the primary one. This enables the cascading filtering behavior where each attribute selection further narrows down the available options for the remaining attributes.

### 6. Rendering Attribute Selectors with Toggle Feedback

```tsx
{/* Primary Attribute Selector (e.g., Size) with toggle feedback */}
<section className={styles.product_variants}>
  <h3>{primaryAttributeTitle}:</h3>
  <div className={styles.variants}>
    {groupedVariants.map((group) => {
      const isSelected = selectedAttributes[primaryAttributeTitle] === group.primaryValue
      return (
        <div
          key={group.primaryValue}
          className={`${isSelected ? `${styles.variant__highlight} ${styles.variant}` : `${styles.variant}`}`}
          onClick={() => handlePrimarySelection(group.primaryValue)}
          title={isSelected ? `Click to deselect ${primaryAttributeTitle} ${group.primaryValue}` : `Click to select ${primaryAttributeTitle} ${group.primaryValue}`}
          style={{ cursor: 'pointer' }}
        >
          <p>{group.primaryValue}</p>
          <p>${group.price.toFixed(2)}</p>
        </div>
      )
    })}
  </div>
</section>

{/* Secondary Attribute Selectors (e.g., Color) */}
{secondaryAttributes.map((selector) => (
  <section key={selector.attribute_id} className={styles.option_selector}>
    <h3 className={styles.selector_title}>{selector.attribute_title}:</h3>
    <div className={styles.selector_values}>
      {selector.values.map(value => {
        const isAvailable = isAttributeValueAvailable(selector.attribute_title, value.value_text)
        return (
          <button
            key={value.value_id}
            className={`
              ${styles.selector_value}
              ${selectedAttributes[selector.attribute_title] === value.value_text ? styles.selected_value : ''}
              ${!isAvailable ? styles.unavailable_value : ''}
            `}
            onClick={() => handleAttributeSelection(selector.attribute_title, value.value_id, value.value_text)}
            disabled={!isAvailable}
            title={!isAvailable ? `Not available with selected ${primaryAttributeTitle}` : value.value_text}
          >
            {selector.attribute_title.toLowerCase() === 'color' ? (
              <div
                className={styles.color_swatch}
                style={{
                  backgroundColor: value.value_text.toLowerCase(),
                  opacity: isAvailable ? 1 : 0.3,
                  boxShadow: selectedAttributes[selector.attribute_title] === value.value_text
                    ? '0 0 0 2px white, 0 0 0 4px ' + value.value_text.toLowerCase()
                    : 'none'
                }}
                title={value.value_text}
              />
            ) : (
              value.value_text
            )}
          </button>
        )
      })}
    </div>
  </section>
))}
```

## State Management

The application uses Zustand for state management:

1. **cartStore**: Manages the selected variant and extra data (selected attribute values).
   - `selectedVariant`: The currently selected product variant.
   - `cartItem.extra_data`: A record of selected attribute values.

```typescript
// In cartStore.ts
const cartStore = create<CartStoreShape>()(
  persist(
    (set) => ({
      selectedVariant: null,
      cartItem: {
        extra_data: {}
      },
      // ...other state properties

      setProductVariant: (product_variant) => set({ selectedVariant: product_variant }),
      setExtraData: (extra_data) => set((state) => ({
        cartItem: { ...state.cartItem, extra_data }
      })),
      resetExtraData: () => set((state) => ({
        cartItem: { ...state.cartItem, extra_data: {} }
      })),
    }),
    {
      name: 'cart_store',
    }
  )
)
```

## Conclusion

The product variant selection functionality provides a dynamic and user-friendly way to navigate complex product options. It ensures that users can only select valid attribute combinations while providing visual feedback about available options.

Key aspects of the implementation include:

- **Cascading filtering** of available attribute values based on all previous selections
- **Progressive narrowing** of options as users make selections (e.g., Size → Color → Material)
- **Toggle functionality** allowing users to select and deselect attributes by clicking the same button
- **Order-based display** ensuring consistent presentation based on variant order values
- **Handling of complex product variants** with multiple attributes
- **Visual indication** of selected and available options with enhanced UI feedback
- **Automatic selection** of matching product variants with lowest order priority
- **State management** using Zustand for persistence

### Example Scenario

Consider a product with Size, Color, and Material attributes:

1. **Initial State**: The first variant (by order) is automatically selected

2. **User selects Size "M"**:
   - Available Colors are filtered to show only "Red" and "Green" (colors available in Size M)
   - Available Materials are filtered based on Size M
   - UI shows Size "M" as selected with visual highlighting

3. **User then selects Color "Green"**:
   - Available Materials are further filtered to show only "Silk" (the only material available for Size M + Color Green)
   - UI shows both Size "M" and Color "Green" as selected

4. **System automatically selects the matching variant** with Size M, Color Green, and Material Silk:
   - Product details (price, stock, images) update to reflect the selected variant
   - The UI visually indicates which options are selected and which are available

5. **Toggle Functionality Examples**:
   - If user clicks Size "M" again: All selections are reset, system reverts to first variant
   - If user clicks Color "Green" again: Only color selection is removed, Size "M" remains selected, and available colors/materials are recalculated
   - Tooltips provide clear feedback: "Click to select" or "Click to deselect" based on current state
