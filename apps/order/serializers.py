from django.db import transaction
from django.contrib.auth import get_user_model
from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from decimal import Decimal
from apps.products.serializers import SimpleProductSerializer, SimpleProductVariantSerializer
from apps.customers.serializers import SimpleCustomerSerializer, AddressSerializer
from apps.payments.serializers import PaymentMethodSerializer
from apps.customers.models import Customer, Address
from apps.payments.models import PaymentOption
from apps.cart.models import Cart, CartItem
from apps.products.signals.signals import low_stock_alert
from utils.shipping_cost import calculate_shipping_cost
from .models import Order, OrderItem
from .signals.signals import order_created
from .utils import create_stripe_payment_intent
from django.db.transaction import on_commit
import stripe

from ..cart.serializers import CartSerializer

User = get_user_model()


# class OrderItemSerializer(ModelSerializer):
#     product = SimpleProductSerializer()
#     product_variant = SimpleProductVariantSerializer()
#     extra_data = serializers.JSONField()
#
#     class Meta:
#         model = OrderItem
#         fields = ['id', 'product', 'product_variant', 'extra_data', 'quantity', 'total_price']
#
#
# class OrderSerializer(ModelSerializer):
#     ordered_items = OrderItemSerializer(many=True)
#     customer = SimpleCustomerSerializer()
#     selected_address = AddressSerializer()
#     payment_method = PaymentMethodSerializer()
#     shipping_cost = serializers.DecimalField(max_digits=6, decimal_places=2, read_only=True)
#
#     class Meta:
#         model = Order
#         fields = ['id', 'customer', 'placed_at', 'payment_status', 'ordered_items',
#                   'selected_address', 'order_status', 'payment_method', 'shipping_cost',
#                   'subtotal', 'total_weight', 'total', 'payment_intent_id']

# Optimized versions:
class OrderItemSerializer(ModelSerializer):
    product = SimpleProductSerializer(read_only=True)
    product_variant = SimpleProductVariantSerializer(read_only=True)
    extra_data = serializers.JSONField()

    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'product_variant', 'extra_data', 'quantity', 'total_price']


class OrderSerializer(ModelSerializer):
    ordered_items = OrderItemSerializer(many=True, read_only=True)
    customer = SimpleCustomerSerializer(read_only=True)
    selected_address = AddressSerializer(read_only=True)
    payment_method = PaymentMethodSerializer(read_only=True)
    shipping_cost = serializers.DecimalField(max_digits=6, decimal_places=2, read_only=True)

    class Meta:
        model = Order
        fields = ['id', 'customer', 'placed_at', 'payment_status', 'ordered_items',
                  'selected_address', 'order_status', 'payment_method', 'shipping_cost',
                  'subtotal', 'total_weight', 'total', 'payment_intent_id']


# class UpdateOrderSerializer(ModelSerializer):
#     class Meta:
#         model = Order
#         fields = ['payment_status']


class CreateOrderSerializer(serializers.Serializer):
    cart_id = serializers.UUIDField()
    # This querying is just for validation
    selected_address = serializers.PrimaryKeyRelatedField(queryset=Address.objects.all())
    order_status = serializers.ChoiceField(choices=Order.ORDER_STATUS, default='Pending')
    payment_method = serializers.PrimaryKeyRelatedField(queryset=PaymentOption.objects.all())

    def validate_cart_id(self, cart_id):
        if not Cart.objects.filter(pk=cart_id).exists():
            raise serializers.ValidationError('No cart with the given ID was found.')
        if CartItem.objects.filter(cart_id=cart_id).count() == 0:
            raise serializers.ValidationError('The cart is empty.')
        return cart_id

    def validate(self, data):
        # Ensure customer has filled out their profile with address, phone number, and date of birth
        customer = Customer.objects.get(user_id=self.context['user_id'])
        # user = User.objects.get(id=self.context['user_id'])
        user = customer.user
        errors = {}
        if not user.phone_number:
            errors["phone_number"] = "Phone number is required."
        if not customer.birth_date:
            errors["birth_date"] = "Date of birth is required."
        if not customer.address.exists():
            errors["address"] = "At least one address is required."
        if errors:
            raise serializers.ValidationError(errors)
        return data

        # if not user.phone_number:
        #     raise serializers.ValidationError("Phone number is required to create an order.")
        #
        # if not customer.birth_date:
        #     raise serializers.ValidationError("Date of birth is required to create an order.")
        #
        # if not customer.address.exists():
        #     raise serializers.ValidationError("At least one address is required to create an order.")
        #
        # return data

    def save(self, **kwargs):
        # Retrieve and validate core data outside transaction to avoid unnecessary locking of the database.
        cart_id = self.validated_data['cart_id']  # Get the cart ID from the validated serializer data.
        customer = Customer.objects.get(user_id=self.context['user_id'])
        selected_address = self.validated_data['selected_address']
        payment_method = self.validated_data['payment_method']
        cart = Cart.objects.get(id=cart_id)
        cart_items = cart.cart_items.all()

        # Pre-compute order totals and costs for efficiency.
        # total_weight = sum(item.quantity * item.product_variant.weight for item in cart_items)
        # shipping_cost = Decimal(calculate_shipping_cost(total_weight))
        # subtotal = sum(item.quantity * item.product_variant.get_total_item_price() for item in cart_items)
        # subtotal = sum(item.quantity * item.product_variant.get_total_item_price() for item in cart_items)

        cart_serializer = CartSerializer(cart)
        grand_total = cart_serializer.data['grand_total']

        subtotal = cart_serializer.data['total_price']
        shipping_cost = cart_serializer.data['shipping_cost']
        total_weight = cart_serializer.data['cart_weight']

        total = grand_total

        # Prepare order items structure before transaction for efficient bulk creation later.
        # OrderItem model has a foreign key to Order model.
        order_items = [
            OrderItem(
                order=None,  # Order is not assigned yet; will be set after order creation.
                product=cart_item.product,
                product_variant=cart_item.product_variant,
                # total_price=cart_item.product_variant.price * cart_item.quantity,
                total_price=cart_item.product_variant.price * cart_item.quantity,
                quantity=cart_item.quantity,
                extra_data=cart_item.extra_data or {}
            )
            for cart_item in cart_items  # Iterate over each cart item to build the order items list.
        ]

        # Use a transaction to ensure atomicity of database operations (all succeed or all fail).
        with transaction.atomic():
            # Create the main order record in the database.
            order = Order.objects.create(
                customer=customer,
                selected_address=selected_address,
                order_status='Pending',
                payment_method=payment_method,
                shipping_cost=shipping_cost,
                subtotal=subtotal,
                total_weight=total_weight,
                total=total
            )

            # Update stock quantities and link order items to the created order.
            for item, cart_item in zip(order_items, cart_items):
                product_variant = cart_item.product_variant  # Get the product variant associated with the cart item.
                if product_variant.stock_qty < cart_item.quantity:
                    raise serializers.ValidationError({
                        "stock_error": f"Insufficient stock for {product_variant.product.title}."
                    })
                product_variant.stock_qty -= cart_item.quantity  # Reduce stock quantity based on cart item quantity.
                product_variant.save()  # Save the updated stock quantity to the database.
                item.order = order  # Link the order item to the created order.

            # Bulk create all order items in one query for performance optimization.
            OrderItem.objects.bulk_create(order_items)

            # Schedule stock alerts for low inventory thresholds after the transaction is committed.
            for cart_item in cart_items:
                product_variant = cart_item.product_variant
                if product_variant.stock_qty in (14, 4):  # Check if stock has reached low thresholds.
                    transaction.on_commit(
                        lambda pv=product_variant: low_stock_alert.send(
                            sender=self.__class__,  # Specify the sender class.
                            product_variant=pv  # Send the product variant with low stock.
                        )
                    )

            # Schedule Stripe payment intent creation if payment method is Stripe.
            if payment_method.slug == 'stripe':
                def create_payment_intent():
                    try:
                        payment_intent = create_stripe_payment_intent(order)
                        order.payment_intent_id = payment_intent['id']
                        order.save()
                        # payment_intent = stripe.PaymentIntent.create(
                        #     amount=int(order.total * 100),  # Stripe requires the amount in cents
                        #     currency='usd',
                        #     # customer=stripe_customer.id,
                        #     metadata={
                        #         'order_id': order.id
                        #     },
                        #     automatic_payment_methods={'enabled': True},
                        #     idempotency_key=f"order_{order.id}_{order.total}",  # Is this really necessary?
                        # )
                        # order.payment_intent_id = payment_intent['id']
                        # order.save()

                        # return payment_intent
                    except stripe.error.StripeError as e:
                        order.payment_status = 'failed'
                        order.save()
                        raise serializers.ValidationError({
                            'stripe_error': str(e)
                        })
                    except Exception:
                        raise serializers.ValidationError({
                            'payment_error': 'An unexpected error occurred. Please try again later.'
                        })

                # Schedule payment intent creation after the transaction is committed.
                transaction.on_commit(create_payment_intent)

            # Schedule cart cleanup to delete the cart after successful transaction.
            transaction.on_commit(
                lambda: Cart.objects.filter(pk=cart_id).delete()
            )

            # Schedule order creation notification after successful transaction.
            transaction.on_commit(
                lambda: order_created.send_robust(self.__class__, order=order)
            )

        return order
