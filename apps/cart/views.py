from django.db.models import Prefetch
from django.utils.timezone import now
from rest_framework.viewsets import GenericViewSet, ModelViewSet
from rest_framework.mixins import CreateModelMixin, RetrieveModelMixin, DestroyModelMixin
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from .models import Cart, CartItem
from .serializers import CartSerializer, AddCartItemSerializer, UpdateCartItemSerializer, CartItemSerializer
from ..products.models import AttributeValue, Discount
import logging

logger = logging.getLogger(__name__)


class CartViewSet(CreateModelMixin,
                  RetrieveModelMixin,
                  DestroyModelMixin,
                  GenericViewSet):
    # queryset = Cart.objects.prefetch_related(
    #     'cart_items__product',
    #     'cart_items__product__product_variant',
    # ).all()
    serializer_class = CartSerializer

    # def get_queryset(self):
    #     return Cart.objects.prefetch_related(
    #         Prefetch('cart_items', queryset=CartItem.objects.annotate(
    #             item_total=F('quantity') * F('product_variant__price')
    #         ).select_related('product', 'product_variant'))
    #     )

    # Query optimized solution:
    def get_queryset(self):
        return Cart.objects.prefetch_related(
            Prefetch(
                'cart_items',
                queryset=CartItem.objects.select_related('product', 'product_variant')
                .prefetch_related(
                    Prefetch(
                        'product_variant__price_label',
                        queryset=AttributeValue.objects.select_related('attribute')
                    ),
                    'product_variant__product_image',
                    Prefetch(
                        'product_variant__discounts',
                        queryset=Discount.objects.filter(
                            is_active=True,
                            start_date__lte=now(),
                            end_date__gte=now()
                        ),
                        to_attr='active_discounts'
                    )
                )
            )
        )


class CartItemViewSet(ModelViewSet):
    http_method_names = ['get', 'post', 'patch', 'delete']

    # Selecting the serializer according to the request method
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return AddCartItemSerializer
        elif self.request.method == 'PATCH':
            return UpdateCartItemSerializer
        return CartItemSerializer

    def get_serializer_context(self):
        return {'cart_id': self.kwargs['cart_pk']}

    def get_queryset(self):
        return CartItem.objects.filter(cart_id=self.kwargs['cart_pk']) \
            .select_related('product', 'product_variant') \
            .prefetch_related(
            'product_variant__price_label',
            'product_variant__product_image',
            Prefetch(
                'product_variant__discounts',
                queryset=Discount.objects.filter(
                    is_active=True,
                    start_date__lte=now(),
                    end_date__gte=now()
                ),
                to_attr='active_discounts'
            )
        )

    def perform_destroy(self, instance):
        """Delete cart item and recalculate shipping"""
        cart = instance.cart
        super().perform_destroy(instance)

        # Trigger shipping recalculation after deleting item
        try:
            from apps.shipping.services import CartShippingService
            cart_shipping_service = CartShippingService()
            cart_shipping_service.recalculate_cart_shipping(cart, force_recalculate=True)
        except Exception as e:
            logger.error(f"Failed to recalculate shipping after item deletion for cart {cart.id}: {e}")

    @action(detail=False, methods=['post'])
    def recalculate_shipping(self, request, cart_pk=None):
        """Manually trigger shipping recalculation for cart"""
        try:
            cart = Cart.objects.get(id=cart_pk)

            from apps.shipping.services import CartShippingService
            cart_shipping_service = CartShippingService()
            result = cart_shipping_service.recalculate_cart_shipping(cart, force_recalculate=True)

            return Response(result, status=status.HTTP_200_OK)

        except Cart.DoesNotExist:
            return Response(
                {'error': 'Cart not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Failed to recalculate shipping for cart {cart_pk}: {e}")
            return Response(
                {'error': 'Failed to recalculate shipping'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
