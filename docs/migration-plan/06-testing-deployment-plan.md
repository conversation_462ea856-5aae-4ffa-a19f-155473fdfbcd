# Testing & Deployment Technical Plan

## Overview

This document outlines the comprehensive testing strategy and deployment pipeline for the Admin Arena dashboard, ensuring reliability, performance, and maintainability.

## Package Versions

```json
{
  "@testing-library/react": "^14.0.0",
  "@testing-library/jest-dom": "^6.1.0",
  "@testing-library/user-event": "^14.5.0",
  "@playwright/test": "^1.40.0",
  "vitest": "^1.0.0",
  "@vitest/ui": "^1.0.0",
  "jsdom": "^23.0.0",
  "msw": "^2.0.0",
  "@types/jest": "^29.5.0"
}
```

## Testing Strategy

### 1. Testing Pyramid

```typescript
interface TestingPyramid {
  unit: {
    percentage: '70%';
    tools: ['Vitest', 'React Testing Library'];
    scope: ['Components', 'Hooks', 'Utils', 'Services'];
  };
  integration: {
    percentage: '20%';
    tools: ['Vitest', 'MSW'];
    scope: ['API Integration', 'Store Integration', 'Component Integration'];
  };
  e2e: {
    percentage: '10%';
    tools: ['Playwright'];
    scope: ['Critical User Journeys', 'Cross-browser Testing'];
  };
}
```

### 2. Unit Testing Configuration

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react-swc';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/routeTree.gen.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
});
```

```typescript
// src/test/setup.ts
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeAll, afterAll } from 'vitest';
import { server } from './mocks/server';

// Start MSW server
beforeAll(() => server.listen());

// Clean up after each test
afterEach(() => {
  cleanup();
  server.resetHandlers();
});

// Close server after all tests
afterAll(() => server.close());

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};
```

### 3. Component Testing Examples

```typescript
// src/components/ui/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Button } from '../Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state', () => {
    render(<Button loading>Loading</Button>);
    
    expect(screen.getByRole('button')).toBeDisabled();
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('applies variant classes correctly', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>);
    expect(screen.getByRole('button')).toHaveClass('primary');

    rerender(<Button variant="secondary">Secondary</Button>);
    expect(screen.getByRole('button')).toHaveClass('secondary');
  });
});
```

```typescript
// src/components/forms/__tests__/FormField.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FormField } from '../FormField';

const schema = z.object({
  email: z.string().email('Invalid email address')
});

const TestForm = () => {
  const { control, handleSubmit } = useForm({
    resolver: zodResolver(schema),
    defaultValues: { email: '' }
  });

  return (
    <form onSubmit={handleSubmit(() => {})}>
      <FormField
        name="email"
        control={control}
        label="Email"
        placeholder="Enter your email"
      />
    </form>
  );
};

describe('FormField', () => {
  it('renders with label and placeholder', () => {
    render(<TestForm />);
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/enter your email/i)).toBeInTheDocument();
  });

  it('shows validation error for invalid input', async () => {
    render(<TestForm />);
    
    const input = screen.getByLabelText(/email/i);
    fireEvent.change(input, { target: { value: 'invalid-email' } });
    fireEvent.blur(input);

    await waitFor(() => {
      expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
    });
  });
});
```

### 4. Hook Testing

```typescript
// src/hooks/__tests__/use-auth-guard.test.tsx
import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useAuthGuard } from '../use-auth-guard';
import { useAuthStore } from '../../stores/auth-store';

// Mock the auth store
vi.mock('../../stores/auth-store');

const mockUseAuthStore = vi.mocked(useAuthStore);

describe('useAuthGuard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns authenticated state for valid user', () => {
    mockUseAuthStore.mockReturnValue({
      isAuthenticated: true,
      checkPermission: vi.fn().mockReturnValue(true),
      hasGroup: vi.fn().mockReturnValue(true),
      user: { id: 1, email: '<EMAIL>', is_superuser: false }
    } as any);

    const { result } = renderHook(() => useAuthGuard({
      permission: 'staff.view_orders'
    }));

    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.hasPermission).toBe(true);
  });

  it('returns false for missing permission', () => {
    mockUseAuthStore.mockReturnValue({
      isAuthenticated: true,
      checkPermission: vi.fn().mockReturnValue(false),
      hasGroup: vi.fn().mockReturnValue(true),
      user: { id: 1, email: '<EMAIL>', is_superuser: false }
    } as any);

    const { result } = renderHook(() => useAuthGuard({
      permission: 'staff.manage_orders'
    }));

    expect(result.current.hasPermission).toBe(false);
  });
});
```

### 5. API Mocking with MSW

```typescript
// src/test/mocks/handlers.ts
import { http, HttpResponse } from 'msw';
import { API_BASE_URL } from '../../config/constants';

export const handlers = [
  // Auth endpoints
  http.post(`${API_BASE_URL}/staff/auth/login/`, async ({ request }) => {
    const body = await request.json() as { email: string; password: string };
    
    if (body.email === '<EMAIL>' && body.password === 'password') {
      return HttpResponse.json({
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: 1,
          email: '<EMAIL>',
          is_staff: true,
          is_superuser: true
        }
      });
    }
    
    return HttpResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    );
  }),

  http.get(`${API_BASE_URL}/staff/auth/user/`, () => {
    return HttpResponse.json({
      user: {
        id: 1,
        email: '<EMAIL>',
        is_staff: true,
        is_superuser: true
      },
      permissions: ['staff.view_orders', 'staff.manage_orders'],
      groups: ['Order Manager']
    });
  }),

  // Orders endpoints
  http.get(`${API_BASE_URL}/staff/orders/orders/`, ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('page_size') || '20');
    
    return HttpResponse.json({
      count: 100,
      next: page < 5 ? `${API_BASE_URL}/staff/orders/orders/?page=${page + 1}` : null,
      previous: page > 1 ? `${API_BASE_URL}/staff/orders/orders/?page=${page - 1}` : null,
      results: Array.from({ length: pageSize }, (_, i) => ({
        id: (page - 1) * pageSize + i + 1,
        order_number: `ORD-${String((page - 1) * pageSize + i + 1).padStart(6, '0')}`,
        customer: {
          id: 1,
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe'
        },
        status: 'PENDING',
        total_amount: 99.99,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))
    });
  }),

  http.patch(`${API_BASE_URL}/staff/orders/orders/:id/update_status/`, ({ params }) => {
    return HttpResponse.json({
      id: parseInt(params.id as string),
      status: 'PROCESSING',
      updated_at: new Date().toISOString()
    });
  })
];
```

```typescript
// src/test/mocks/server.ts
import { setupServer } from 'msw/node';
import { handlers } from './handlers';

export const server = setupServer(...handlers);
```

### 6. Integration Testing

```typescript
// src/pages/orders/__tests__/OrdersListPage.integration.test.tsx
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach } from 'vitest';
import { OrdersListPage } from '../OrdersListPage';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false }
  }
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('OrdersListPage Integration', () => {
  beforeEach(() => {
    // Reset any mocks or state before each test
  });

  it('loads and displays orders list', async () => {
    renderWithProviders(<OrdersListPage filters={{}} />);

    // Show loading state initially
    expect(screen.getByText(/loading/i)).toBeInTheDocument();

    // Wait for orders to load
    await waitFor(() => {
      expect(screen.getByText(/ORD-000001/)).toBeInTheDocument();
    });

    // Check that multiple orders are displayed
    expect(screen.getAllByText(/ORD-/)).toHaveLength(20);
  });

  it('handles order status update', async () => {
    renderWithProviders(<OrdersListPage filters={{}} />);

    await waitFor(() => {
      expect(screen.getByText(/ORD-000001/)).toBeInTheDocument();
    });

    // Click on first order to open details
    fireEvent.click(screen.getByText(/ORD-000001/));

    // Update status
    const statusSelect = screen.getByLabelText(/status/i);
    fireEvent.change(statusSelect, { target: { value: 'PROCESSING' } });

    const updateButton = screen.getByText(/update status/i);
    fireEvent.click(updateButton);

    // Verify success message
    await waitFor(() => {
      expect(screen.getByText(/status updated successfully/i)).toBeInTheDocument();
    });
  });
});
```

## End-to-End Testing

### 1. Playwright Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }]
  ],
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI
  }
});
```

### 2. E2E Test Examples

```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test('should login successfully with valid credentials', async ({ page }) => {
    await page.goto('/login');

    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password');
    await page.click('[data-testid="login-button"]');

    await expect(page).toHaveURL('/');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.goto('/login');

    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');

    await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials');
  });
});
```

```typescript
// e2e/orders.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Order Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password');
    await page.click('[data-testid="login-button"]');
    await expect(page).toHaveURL('/');
  });

  test('should display orders list', async ({ page }) => {
    await page.goto('/orders');

    await expect(page.locator('[data-testid="orders-table"]')).toBeVisible();
    await expect(page.locator('[data-testid="order-row"]')).toHaveCount(20);
  });

  test('should update order status', async ({ page }) => {
    await page.goto('/orders');

    // Click on first order
    await page.click('[data-testid="order-row"]:first-child');

    // Update status
    await page.selectOption('[data-testid="status-select"]', 'PROCESSING');
    await page.click('[data-testid="update-status-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Status updated');
  });

  test('should perform bulk status update', async ({ page }) => {
    await page.goto('/orders');

    // Select multiple orders
    await page.check('[data-testid="select-all-checkbox"]');
    
    // Open bulk actions
    await page.click('[data-testid="bulk-actions-button"]');
    await page.click('[data-testid="bulk-update-status"]');
    
    // Select new status
    await page.selectOption('[data-testid="bulk-status-select"]', 'PROCESSING');
    await page.click('[data-testid="confirm-bulk-update"]');

    // Verify success
    await expect(page.locator('[data-testid="bulk-success-message"]')).toBeVisible();
  });
});
```

## Deployment Pipeline

### 1. GitHub Actions Workflow

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run unit tests
        run: npm run test:unit -- --coverage
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
        env:
          VITE_API_BASE_URL: ${{ secrets.API_BASE_URL }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist/
      
      - name: Deploy to staging
        run: |
          # Deploy to staging environment
          aws s3 sync dist/ s3://admin-arena-staging --delete
          aws cloudfront create-invalidation --distribution-id ${{ secrets.STAGING_CLOUDFRONT_ID }} --paths "/*"

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist/
      
      - name: Deploy to production
        run: |
          # Deploy to production environment
          aws s3 sync dist/ s3://admin-arena-prod --delete
          aws cloudfront create-invalidation --distribution-id ${{ secrets.PROD_CLOUDFRONT_ID }} --paths "/*"
```

### 2. Environment Configuration

```typescript
// src/config/environment.ts
interface Environment {
  API_BASE_URL: string;
  SENTRY_DSN?: string;
  ANALYTICS_ID?: string;
  ENVIRONMENT: 'development' | 'staging' | 'production';
}

export const environment: Environment = {
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  SENTRY_DSN: import.meta.env.VITE_SENTRY_DSN,
  ANALYTICS_ID: import.meta.env.VITE_ANALYTICS_ID,
  ENVIRONMENT: (import.meta.env.VITE_ENVIRONMENT as Environment['ENVIRONMENT']) || 'development'
};
```

## Performance Monitoring

### 1. Bundle Analysis

```json
{
  "scripts": {
    "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"
  }
}
```

### 2. Lighthouse CI

```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI

on:
  pull_request:
    branches: [main]

jobs:
  lighthouse:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build app
        run: npm run build
      
      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
```

## Next Steps

1. **Test Implementation**: Write comprehensive test suites for all components and features
2. **CI/CD Setup**: Configure GitHub Actions workflows for automated testing and deployment
3. **Monitoring**: Set up error tracking, performance monitoring, and analytics
4. **Documentation**: Create testing guidelines and deployment procedures
5. **Quality Gates**: Establish code coverage thresholds and quality metrics
