@use '../src/styles/reset.scss';
@use '../src/styles/variables.scss' as *;
@use '../src/styles/mixins.scss' as *;
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap');

html {
  font-family: $primary-font-family;
}

body {
  font-family: $primary-font-family;
  color: $primary-dark-text-color;
  background-color: white;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Global utility classes
.container {
  @include container;
}

.logo_header {
  display: flex;
  justify-content: center;
  background-color: $primary-dark;
  padding: 10px 0;
}

.title {
  font-size: 25px;
  font-weight: bold;
  text-align: center;
  margin: 1rem 0;
  color: $primary-dark-blue;
}

.loading_svg {
  margin: 2px 10px;
  width: 20px;
  height: 20px;
}

// Password container styling
.password__container {
  position: relative;
  display: flex;
  align-items: center;

  input {
    width: 100%;
    padding-right: 40px;
  }

  span {
    position: absolute;
    right: 10px;
    cursor: pointer;
    color: $gray-500;

    &:hover {
      color: $primary-dark-text-color;
    }

    i {
      display: flex;
      align-items: center;
    }
  }
}

// Form styling
.form {
  @include flexbox(flex-start, stretch, column);
  row-gap: 15px;
}

.form_group {
  @include flexbox(flex-start, stretch, column);
  row-gap: 4px;
}

.form_label {
  font-weight: bold;
  color: $primary-dark-blue;
  display: block;
}

.form_input {
  border: .1px solid $primary-dark-text-color;
  border-radius: 3px;
  padding: 8px 10px;
  font-size: 16.5px;
  width: 100%;
  box-sizing: border-box;

  &:focus {
    outline: 2px solid $primary-blue-light;
    border: none;
  }
}

.form_error {
  color: $error;
  text-align: left;
  margin-top: 4px;
  font-size: $font-size-1;
}