@use '../../../src/styles/variables.scss' as *;
@use '../../../src/styles/mixins.scss' as *;

.loginPage {
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: $spacing-8;
}

.title {
  font-size: $font-size-5;
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.subtitle {
  color: $primary-lighter-text-color;
  margin: 0;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-5;
}

.passwordToggle {
  background: none;
  border: none;
  color: $gray-500;
  cursor: pointer;
  padding: 0;

  &:hover {
    color: $primary-dark-text-color;
  }
}

.errorMessage {
  background-color: $error;
  color: $primary-red;
  padding: $spacing-3 $spacing-4;
  border-radius: $border-radius-md;
  font-size: $font-size-1;
  text-align: center;
}

.forgotPassword {
  text-align: right;
  margin-top: -#{$spacing-3};
}

.forgotLink {
  color: $primary-blue;
  font-size: $font-size-1;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.footer {
  margin-top: $spacing-6;
  text-align: center;
}

.signupPrompt {
  color: $primary-lighter-text-color;
  margin: 0;
}

.signupLink {
  color: $primary-blue;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    text-decoration: underline;
  }
}