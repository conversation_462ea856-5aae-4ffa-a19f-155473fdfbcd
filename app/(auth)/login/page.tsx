'use client'

// Login page with form validation and authentication
// <PERSON>les user login and redirects on success

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useForm, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import Logo from '../../../src/components/utils/logo/Logo'
import Alert from '../../../src/components/utils/alert/Alert'
import { getErrorMessage } from '../../../src/components/utils/getErrorMessage'
import useTogglePasswordVisibility from '../../../src/hooks/useTogglePasswordVisibility'
import useLogin from '../../../src/hooks/auth/useLogin'
import loading from '../../../src/assets/loading_svg_white.svg'
import styles from './page.module.scss'

// Email validation schema
const emailSchema = z.string().email()

// Phone number validation schema (supports international formats)
const phoneNumberSchema = z.string().regex(
  /^(\+\d{1,3}[- ]?)?\d{10}$/,
  'Invalid phone number format'
)

const loginSchema = z.object({
  username: z
    .string()
    .min(1, { message: "Enter a valid email or phone number." })
    .transform((value) => value.replace(/\s+/g, ''))
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number",
      }
    ),
  password: z.string().min(1, 'Password is required'),
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginPage() {
  const router = useRouter()
  const { isVisible, toggleVisibility } = useTogglePasswordVisibility()
  const { mutation } = useLogin()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit: SubmitHandler<LoginFormData> = async (data) => {
    mutation.mutate(data, {
      onSuccess: () => {
        // Don't redirect immediately - show success state like React-TS version
        // The success notification is handled in the hook
      }
    })
  }

  return (
    <div className={styles.login_container}>
      <div className={`${styles.form_container} container`}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.isSuccess ? (
          <div>
            <Alert variant="success" message='Login successful.' textAlign='center' />
            <div className={styles.login_nav}>
              <h3>Navigate me to:</h3>
              <div>
                <Link href='/checkout'>Continue Checkout</Link>
                <Link href='/customer'>Update Profile</Link>
                <Link href='/cart'>Shopping Cart</Link>
              </div>
            </div>
          </div>
        ) : (
          <div>
            {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error)} />}
            <h2 className='title'>Login</h2>
            <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
              <div>
                <label htmlFor="username">Email or phone number:</label>
                <input
                  placeholder={` Eg: +94789999999`}
                  type="text"
                  id="username"
                  {...register("username")}
                />
                {errors.username && <p>{errors.username.message}</p>}
              </div>
              <div>
                <div className={styles.password__reset}>
                  <label htmlFor="password">Password:</label>
                  <Link href='/auth/password-reset'>Forget password?</Link>
                </div>
                <section className='password__container'>
                  <input
                    type={isVisible ? "text" : "password"}
                    id="password"
                    {...register("password")}
                  />
                  <span onClick={toggleVisibility}>
                    <i>{isVisible ? <FaEyeSlash /> : <FaEye />}</i>
                  </span>
                </section>
                {errors.password && <p>{errors.password.message}</p>}
              </div>
              <button className={styles.login_btn} type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? (
                  <img src={loading} alt="Loading..." className='loading_svg' />
                ) : (
                  'Login'
                )}
              </button>
              <p className={styles.login_or_register}>
                Don't have an account yet? <Link href='/auth/register'>Register</Link>
              </p>
            </form>
          </div>
        )}
      </div>
    </div>
  )
}
