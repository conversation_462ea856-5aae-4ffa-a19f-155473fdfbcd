@use '../../../src/styles/variables.scss' as *;
@use '../../../src/styles/mixins.scss' as *;

.registerPage {
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: $spacing-8;
}

.title {
  font-size: $font-size-5;
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
}

.subtitle {
  color: $primary-lighter-text-color;
  margin: 0;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-5;
}

.nameFields {
  // @include grid(2);
  gap: $spacing-4;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.passwordToggle {
  background: none;
  border: none;
  color: $gray-500;
  cursor: pointer;
  padding: 0;

  &:hover {
    color: $primary-dark-text-color;
  }
}

.footer {
  margin-top: $spacing-6;
  text-align: center;
}

.loginPrompt {
  color: $primary-lighter-text-color;
  margin: 0;
}

.loginLink {
  color: $primary-blue;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    text-decoration: underline;
  }
}