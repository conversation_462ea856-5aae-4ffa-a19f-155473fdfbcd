'use client'

// Register page with form validation and account creation
// Handles user registration and redirects on success

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useForm, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Logo from '../../../'
import Alert from '../../'
import { getErrorMessage } from '../../../src/components/utils/getErrorMessage'
import useRegister from '../../../src/hooks/auth/useRegister'
import loading from '../../../src/assets/loading_svg_white.svg'
import styles from './page.module.scss'

// Email validation schema
const emailSchema = z.string().email()

// Phone number validation schema (supports international formats)
const phoneNumberSchema = z.string().regex(
  /^(\+\d{1,3}[- ]?)?\d{10}$/,
  'Invalid phone number format'
)

const registerSchema = z.object({
  username: z
    .string()
    .min(1, { message: "Email or phone number is required" })
    .transform((value) => value.replace(/\s+/g, ''))
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number",
      }
    ),
})

type RegisterFormData = z.infer<typeof registerSchema>

export default function RegisterPage() {
  const router = useRouter()
  const [isPhoneInput, setIsPhoneInput] = useState(false)
  const [usernameValue, setUsernameValue] = useState('')
  const { mutation } = useRegister()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  })

  // Handle input change and sync with form state
  const handleInputChange = (value: string) => {
    setUsernameValue(value)
    setValue("username", value)

    // Detect if input looks like a phone number
    const phonePattern = /^[\+]?[0-9]/
    setIsPhoneInput(phonePattern.test(value))
  }

  const onSubmit: SubmitHandler<RegisterFormData> = async (data) => {
    mutation.mutate(data, {
      onSuccess: () => {
        // Success notification is handled in the hook
      }
    })
  }

  return (
    <div className={styles.register_container}>
      <div className={`${styles.form_container} container`}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.isSuccess ? (
          <div>
            <Alert variant="success" message='Registration initiated successfully. Please check your email/phone for verification code.' textAlign='center' />
            <div className={styles.register_nav}>
              <h3>Next Steps:</h3>
              <div>
                <Link href='/auth/verify'>Verify Account</Link>
                <Link href='/auth/login'>Back to Login</Link>
              </div>
            </div>
          </div>
        ) : (
          <div>
            {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error)} />}
            <h2 className='title'>Create your account</h2>
            <form onSubmit={handleSubmit(onSubmit)} className='form'>
              <div className='form_group'>
                <label className='form_label' htmlFor="username">Email or Phone number:</label>
                <div className={styles.form_group}>
                  <input
                    className='form_input'
                    placeholder={isPhoneInput ? 'Enter phone number' : 'Enter email address'}
                    type="text"
                    id="username"
                    value={usernameValue}
                    onChange={(e) => handleInputChange(e.target.value)}
                    {...register("username")}
                  />
                  {errors.username && <p className='form_error'>{errors.username.message}</p>}
                </div>
                <div className={styles.input_type_toggle}>
                  <button
                    type="button"
                    onClick={() => {
                      setIsPhoneInput(!isPhoneInput)
                      setUsernameValue('')
                      setValue("username", '')
                    }}
                    className={styles.toggle_btn}
                  >
                    Use {isPhoneInput ? 'email' : 'phone number'} instead
                  </button>
                </div>
              </div>

              <button className={styles.register_btn} type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? (
                  // <img src={loading} alt="Loading..." className='loading_svg' />
                  <p>Registering...</p>
                ) : (
                  'Create Account'
                )}
              </button>

              <p className={styles.login_or_register}>
                Already have an account? <Link href='/auth/login'>Sign in</Link>
              </p>
            </form>
          </div>
        )}
      </div>
    </div>
  )
}
