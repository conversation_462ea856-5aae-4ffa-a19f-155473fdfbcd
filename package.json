{"name": "admin-arena", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tanstack/query-sync-storage-persister": "^5.82.0", "@tanstack/react-query": "^5.82.0", "@tanstack/react-query-devtools": "^5.82.0", "@tanstack/react-query-persist-client": "^5.82.0", "@tanstack/react-router": "^1.121.34", "@tanstack/react-router-devtools": "^1.129.8", "@types/react-dnd-html5-backend": "^3.0.2", "@types/react-dropzone": "^5.1.0", "@types/react-select": "^5.0.1", "axios": "^1.10.0", "js-cookie": "^3.0.5", "luxon": "^3.7.1", "react": "^19.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-select": "^5.10.2", "react-spinners": "^0.17.0", "zod": "^4.0.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tanstack/router-plugin": "^1.121.34", "@types/js-cookie": "^3.0.6", "@types/luxon": "^3.6.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "sass": "^1.89.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}