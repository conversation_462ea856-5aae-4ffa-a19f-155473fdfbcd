// Customer service for API interactions
// Handles all customer-related API calls

import APIClient from './api-client'
import { 
  Customer, 
  CustomerFilters, 
  PaginatedResponse, 
  Address,
  Order 
} from '../types/api-types'

// API endpoints
const ENDPOINTS = {
  CUSTOMERS: '/api/staff/customers/',
  ADDRESSES: '/api/staff/customers/addresses/',
  ANALYTICS: '/api/staff/customers/analytics/',
  SEGMENTS: '/api/staff/customers/segments/',
} as const

class CustomerServiceClass {
  private customersClient = new APIClient<Customer>(ENDPOINTS.CUSTOMERS)
  private addressesClient = new APIClient<Address>(ENDPOINTS.ADDRESSES)

  /**
   * Get paginated customers list with filters
   */
  getCustomers = async (filters?: CustomerFilters): Promise<PaginatedResponse<Customer>> => {
    const params = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value))
        }
      })
    }

    return this.customersClient.getAll({
      params,
    })
  }

  /**
   * Get single customer by ID
   */
  getCustomer = async (id: number): Promise<Customer> => {
    const client = new APIClient<Customer>(`${ENDPOINTS.CUSTOMERS}${id}/`)
    return client.get()
  }

  /**
   * Update existing customer
   */
  updateCustomer = async (id: number, customerData: Partial<Customer>): Promise<Customer> => {
    const client = new APIClient<Customer>(`${ENDPOINTS.CUSTOMERS}${id}/`)
    return client.patch(customerData)
  }

  /**
   * Get customer addresses
   */
  getCustomerAddresses = async (customerId: number): Promise<Address[]> => {
    const response = await this.addressesClient.getAll({
      params: { customer: customerId },
    })
    return response.results
  }

  /**
   * Create new customer address
   */
  createCustomerAddress = async (customerId: number, addressData: Partial<Address>): Promise<Address> => {
    return this.addressesClient.post({
      ...addressData,
      customer: customerId,
    })
  }

  /**
   * Update customer address
   */
  updateCustomerAddress = async (
    customerId: number, 
    addressId: number, 
    addressData: Partial<Address>
  ): Promise<Address> => {
    const client = new APIClient<Address>(`${ENDPOINTS.ADDRESSES}${addressId}/`)
    return client.patch(addressData)
  }

  /**
   * Delete customer address
   */
  deleteCustomerAddress = async (customerId: number, addressId: number): Promise<void> => {
    const client = new APIClient<Address>(`${ENDPOINTS.ADDRESSES}${addressId}/`)
    return client.delete()
  }

  /**
   * Get customer orders
   */
  getCustomerOrders = async (customerId: number): Promise<Order[]> => {
    const client = new APIClient<Order>(`${ENDPOINTS.CUSTOMERS}${customerId}/orders/`)
    const response = await client.getAll()
    return response.results
  }

  /**
   * Get customer activity/support history
   */
  getCustomerActivity = async (customerId: number): Promise<any[]> => {
    const client = new APIClient<any>(`${ENDPOINTS.CUSTOMERS}${customerId}/activity/`)
    const response = await client.getAll()
    return response.results
  }

  /**
   * Get customer analytics
   */
  getCustomerAnalytics = async (): Promise<{
    totalCustomers: number
    activeCustomers: number
    newThisMonth: number
    averageOrderValue: number
    topCustomers: Customer[]
    segmentDistribution: Array<{ segment: string; count: number }>
  }> => {
    const client = new APIClient<any>(ENDPOINTS.ANALYTICS)
    return client.get()
  }

  /**
   * Get customer segments
   */
  getCustomerSegments = async (): Promise<Array<{
    id: string
    name: string
    description: string
    customerCount: number
    criteria: Record<string, any>
  }>> => {
    const client = new APIClient<any>(ENDPOINTS.SEGMENTS)
    const response = await client.getAll()
    return response.results
  }

  /**
   * Bulk operations on customers
   */
  bulkOperation = async (data: {
    action: 'activate' | 'deactivate' | 'export' | 'segment'
    customerIds: number[]
    payload?: Record<string, any>
  }): Promise<{ success: boolean; message: string }> => {
    const client = new APIClient<{ success: boolean; message: string }>(`${ENDPOINTS.CUSTOMERS}bulk/`)
    return client.post(data)
  }

  /**
   * Export customers data
   */
  exportCustomers = async (filters?: CustomerFilters): Promise<Blob> => {
    const params = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value))
        }
      })
    }

    const client = new APIClient<Blob>(`${ENDPOINTS.CUSTOMERS}export/`)
    return client.get({
      params,
      responseType: 'blob',
    })
  }
}

export const CustomerService = new CustomerServiceClass()
