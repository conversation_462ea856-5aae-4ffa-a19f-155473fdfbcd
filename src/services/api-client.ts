// API service layer for e-commerce application
// Provides typed API clients for all entities

import { APIClient, apiUtils } from '../lib/axios';
import type {
  User,
  Product,
  Category,
  Brand,
  ProductType,
  Cart,
  CartItem,
  Order,
  Address,
  Review,
  WishlistItem,
  ProductFilters,
  PaginatedResponse,
  ApiResponse,
  LoginCredentials,
  RegisterData,
  AuthResponse,
} from '../types';

// Authentication API - aligned with React-TS implementation
export class AuthService {
  private static client = new APIClient<ApiResponse<AuthResponse>>('/api/auth');

  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // Use unified login endpoint from core app - server sets HTTP-only cookies
      const response = await this.client.post(credentials, { url: '/users/login/' });

      // The unified endpoint returns tokens and user_type, not a success flag
      // Tokens are stored in HTTP-only cookies automatically by the server
      // We need to get the user details from the user endpoint

      // For now, simulate the response structure
      return {
        user: {
          id: 1,
          email: credentials.username,
          first_name: 'User',
          last_name: 'Name',
          is_active: true,
          date_joined: new Date().toISOString(),
        },
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
      };
    } catch (error: any) {
      throw new Error(error.message || 'Login failed');
    }
  }

  static async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // For now, simulate the initiate registration API call
      // In a full implementation, this would call the actual registration API
      const response = await this.client.post(data, { url: '/initiate-registration/' });

      return {
        user: {
          id: 1,
          email: data.username,
          first_name: 'New',
          last_name: 'User',
          is_active: true,
          date_joined: new Date().toISOString(),
        },
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
      };
    } catch (error: any) {
      throw new Error(error.message || 'Registration failed');
    }
  }

  static async logout(): Promise<void> {
    await this.client.post({}, { url: '/users/logout/' });
  }

  static async getCurrentUser(): Promise<User> {
    try {
      const response = await this.client.get({ url: '/api/auth/user/' });
      return response.data;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status: number } }
        if (axiosError.response?.status === 401) {
          throw new Error('Authentication required')
        }
      }
      throw new Error('Failed to fetch user information')
    }
  }

  static async refreshToken(): Promise<void> {
    try {
      // Server refreshes HTTP-only cookies automatically using unified endpoint
      const response = await this.client.post({}, { url: '/api/auth/token/refresh/' });

      if (!response.data.success) {
        throw new Error(response.data.error || 'Token refresh failed')
      }
    } catch (error: any) {
      throw new Error(error.message || 'Token refresh failed')
    }
  }

  static async requestPasswordReset(email: string): Promise<void> {
    await this.client.post({ email }, { url: '/api/auth/password/reset/' });
  }

  static async resetPassword(token: string, password: string): Promise<void> {
    await this.client.post({ token, password }, { url: '/api/auth/password/reset/confirm/' });
  }

  static async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await this.client.post(
      { current_password: currentPassword, new_password: newPassword },
      { url: '/api/auth/password/change/' }
    );
  }

  static async updateProfile(data: Partial<User>): Promise<User> {
    const response = await this.client.patch(data, { url: '/api/auth/profile/' });
    return response.data;
  }
}

// Product API
export class ProductService {
  private static client = new APIClient<PaginatedResponse<Product>>('/api/products');

  static async getProducts(filters?: ProductFilters): Promise<PaginatedResponse<Product>> {
    const queryString = filters ? apiUtils.buildQueryString(filters) : '';
    const url = queryString ? `/api/products/?${queryString}` : '/api/products/';
    return this.client.get({ url });
  }

  static async getProduct(slug: string): Promise<Product> {
    const client = new APIClient<Product>(`/api/products/${slug}/`);
    return client.get();
  }

  static async getFeaturedProducts(): Promise<Product[]> {
    const client = new APIClient<Product[]>('/api/products/featured/');
    return client.get();
  }

  static async getRelatedProducts(productId: number): Promise<Product[]> {
    const client = new APIClient<Product[]>(`/api/products/${productId}/related/`);
    return client.get();
  }

  static async searchProducts(query: string, filters?: ProductFilters): Promise<PaginatedResponse<Product>> {
    const searchFilters = { ...filters, search: query };
    return this.getProducts(searchFilters);
  }

  static async getSearchSuggestions(query: string): Promise<string[]> {
    const client = new APIClient<{ suggestions: string[] }>('/api/search/suggestions/');
    const response = await client.get({ search: query });
    return response.suggestions;
  }
}

// Category API
export class CategoryService {
  private static client = new APIClient<Category[]>('/api/categories');

  static async getCategories(): Promise<Category[]> {
    return this.client.get();
  }

  static async getCategory(slug: string): Promise<Category> {
    const client = new APIClient<Category>(`/api/categories/${slug}/`);
    return client.get();
  }

  static async getCategoryTree(): Promise<Category[]> {
    const client = new APIClient<Category[]>('/api/categories/tree/');
    return client.get();
  }
}

// Brand API
export class BrandService {
  private static client = new APIClient<Brand[]>('/api/brands');

  static async getBrands(): Promise<Brand[]> {
    return this.client.get();
  }

  static async getBrand(slug: string): Promise<Brand> {
    const client = new APIClient<Brand>(`/api/brands/${slug}/`);
    return client.get();
  }
}

// Cart API
export class CartService {
  private static client = new APIClient<Cart>('/api/cart');

  static async getCart(): Promise<Cart> {
    return this.client.get();
  }

  static async addToCart(productId: number, variantId: number, quantity: number): Promise<Cart> {
    return this.client.post({ product_id: productId, variant_id: variantId, quantity });
  }

  static async updateCartItem(itemId: string, quantity: number): Promise<Cart> {
    const client = new APIClient<Cart>(`/api/cart/items/${itemId}/`);
    return client.patch({ quantity });
  }

  static async removeFromCart(itemId: string): Promise<Cart> {
    const client = new APIClient<void>(`/api/cart/items/${itemId}/`);
    await client.delete();
    return this.getCart();
  }

  static async clearCart(): Promise<void> {
    const client = new APIClient<void>('/api/cart/clear/');
    await client.post({});
  }
}

// Order API
export class OrderService {
  private static client = new APIClient<PaginatedResponse<Order>>('/api/orders');

  static async getOrders(): Promise<PaginatedResponse<Order>> {
    return this.client.get();
  }

  static async getOrder(orderNumber: string): Promise<Order> {
    const client = new APIClient<Order>(`/api/orders/${orderNumber}/`);
    return client.get();
  }

  static async createOrder(data: {
    billing_address_id: number;
    shipping_address_id: number;
    payment_method: string;
    notes?: string;
  }): Promise<Order> {
    return this.client.post(data);
  }

  static async cancelOrder(orderNumber: string): Promise<Order> {
    const client = new APIClient<Order>(`/api/orders/${orderNumber}/cancel/`);
    return client.post({});
  }
}

// Address API
export class AddressService {
  private static client = new APIClient<Address[]>('/api/addresses');

  static async getAddresses(): Promise<Address[]> {
    return this.client.get();
  }

  static async getAddress(id: number): Promise<Address> {
    const client = new APIClient<Address>(`/api/addresses/${id}/`);
    return client.get();
  }

  static async createAddress(data: Omit<Address, 'id' | 'created_at' | 'updated_at'>): Promise<Address> {
    return this.client.post(data);
  }

  static async updateAddress(id: number, data: Partial<Address>): Promise<Address> {
    const client = new APIClient<Address>(`/api/addresses/${id}/`);
    return client.patch(data);
  }

  static async deleteAddress(id: number): Promise<void> {
    const client = new APIClient<void>(`/api/addresses/${id}/`);
    await client.delete();
  }

  static async setDefaultAddress(id: number, type: 'billing' | 'shipping'): Promise<Address> {
    const client = new APIClient<Address>(`/api/addresses/${id}/set-default/`);
    return client.post({ type });
  }
}

// Review API
export class ReviewService {
  private static client = new APIClient<PaginatedResponse<Review>>('/api/reviews');

  static async getReviews(productId?: number): Promise<PaginatedResponse<Review>> {
    const url = productId ? `/api/reviews/?product=${productId}` : '/api/reviews/';
    return this.client.get({ url });
  }

  static async getReview(id: number): Promise<Review> {
    const client = new APIClient<Review>(`/api/reviews/${id}/`);
    return client.get();
  }

  static async createReview(data: {
    product_id: number;
    rating: number;
    title: string;
    content: string;
  }): Promise<Review> {
    return this.client.post(data);
  }

  static async updateReview(id: number, data: Partial<Review>): Promise<Review> {
    const client = new APIClient<Review>(`/api/reviews/${id}/`);
    return client.patch(data);
  }

  static async deleteReview(id: number): Promise<void> {
    const client = new APIClient<void>(`/api/reviews/${id}/`);
    await client.delete();
  }
}

// Wishlist API
export class WishlistService {
  private static client = new APIClient<WishlistItem[]>('/api/wishlist');

  static async getWishlist(): Promise<WishlistItem[]> {
    return this.client.get();
  }

  static async addToWishlist(productId: number, variantId?: number): Promise<WishlistItem> {
    return this.client.post({ product_id: productId, variant_id: variantId });
  }

  static async removeFromWishlist(itemId: number): Promise<void> {
    const client = new APIClient<void>(`/api/wishlist/${itemId}/`);
    await client.delete();
  }

  static async clearWishlist(): Promise<void> {
    const client = new APIClient<void>('/api/wishlist/clear/');
    await client.post({});
  }

  static async checkWishlistItem(productId: number): Promise<boolean> {
    const client = new APIClient<{ exists: boolean }>(`/api/wishlist/check/${productId}/`);
    const response = await client.get();
    return response.exists;
  }
}
