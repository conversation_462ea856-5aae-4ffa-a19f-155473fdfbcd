// Analytics service for API interactions
// Handles all analytics-related API calls

import APIClient from './api-client'

// API endpoints
const ENDPOINTS = {
  DASHBOARD: '/api/staff/analytics/dashboard/',
  SALES: '/api/staff/analytics/sales/',
  PRODUCTS: '/api/staff/analytics/products/',
  CUSTOMERS: '/api/staff/analytics/customers/',
  PERFORMANCE: '/api/staff/analytics/performance/',
  REPORTS: '/api/staff/analytics/reports/',
} as const

interface DashboardAnalytics {
  stats: {
    revenue: number
    orders: number
    customers: number
    products: number
    newCustomers: number
    averageOrderValue: number
    conversionRate: number
    customerRetention: number
    returnRate: number
    pendingOrders: number
    processingOrders: number
    completedOrders: number
  }
  trends: {
    revenue: number
    orders: number
    customers: number
    products: number
  }
  charts: {
    salesOverTime: Array<{ date: string; value: number }>
    topProducts: Array<{ name: string; sales: number }>
    customerGrowth: Array<{ date: string; count: number }>
    orderStatusDistribution: Array<{ status: string; count: number }>
  }
}

interface SalesAnalytics {
  totalRevenue: number
  totalOrders: number
  averageOrderValue: number
  revenueByPeriod: Array<{ period: string; revenue: number; orders: number }>
  topProducts: Array<{ 
    id: number
    name: string
    revenue: number
    orders: number
    quantity: number
  }>
  salesByCategory: Array<{ category: string; revenue: number }>
  salesByRegion: Array<{ region: string; revenue: number }>
}

interface ProductAnalytics {
  totalProducts: number
  activeProducts: number
  topSellingProducts: Array<{
    id: number
    name: string
    sales: number
    revenue: number
  }>
  lowStockProducts: Array<{
    id: number
    name: string
    stock: number
  }>
  categoryPerformance: Array<{
    category: string
    products: number
    sales: number
    revenue: number
  }>
  brandPerformance: Array<{
    brand: string
    products: number
    sales: number
    revenue: number
  }>
}

interface CustomerAnalytics {
  totalCustomers: number
  activeCustomers: number
  newCustomers: number
  customerGrowth: Array<{ date: string; count: number }>
  customerSegments: Array<{ segment: string; count: number; revenue: number }>
  topCustomers: Array<{
    id: number
    name: string
    email: string
    orders: number
    totalSpent: number
  }>
  customerRetentionRate: number
  averageCustomerLifetime: number
}

class AnalyticsServiceClass {
  /**
   * Get dashboard analytics
   */
  getDashboardAnalytics = async (period?: string): Promise<DashboardAnalytics> => {
    const client = new APIClient<DashboardAnalytics>(ENDPOINTS.DASHBOARD)
    return client.get({
      params: period ? { period } : undefined,
    })
  }

  /**
   * Get sales analytics
   */
  getSalesAnalytics = async (period?: string): Promise<SalesAnalytics> => {
    const client = new APIClient<SalesAnalytics>(ENDPOINTS.SALES)
    return client.get({
      params: period ? { period } : undefined,
    })
  }

  /**
   * Get product analytics
   */
  getProductAnalytics = async (period?: string): Promise<ProductAnalytics> => {
    const client = new APIClient<ProductAnalytics>(ENDPOINTS.PRODUCTS)
    return client.get({
      params: period ? { period } : undefined,
    })
  }

  /**
   * Get customer analytics
   */
  getCustomerAnalytics = async (period?: string): Promise<CustomerAnalytics> => {
    const client = new APIClient<CustomerAnalytics>(ENDPOINTS.CUSTOMERS)
    return client.get({
      params: period ? { period } : undefined,
    })
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics = async (metric?: string): Promise<{
    metric: string
    value: number
    trend: number
    target?: number
    data: Array<{ date: string; value: number }>
  }> => {
    const client = new APIClient<any>(ENDPOINTS.PERFORMANCE)
    return client.get({
      params: metric ? { metric } : undefined,
    })
  }

  /**
   * Get available reports
   */
  getAvailableReports = async (): Promise<Array<{
    id: string
    name: string
    description: string
    type: 'sales' | 'products' | 'customers' | 'inventory'
    lastGenerated?: string
  }>> => {
    const client = new APIClient<any>(ENDPOINTS.REPORTS)
    const response = await client.getAll()
    return response.results
  }

  /**
   * Generate report
   */
  generateReport = async (reportId: string, params?: Record<string, any>): Promise<{
    reportId: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    downloadUrl?: string
    generatedAt?: string
  }> => {
    const client = new APIClient<any>(`${ENDPOINTS.REPORTS}${reportId}/generate/`)
    return client.post(params || {})
  }

  /**
   * Download report
   */
  downloadReport = async (reportId: string): Promise<Blob> => {
    const client = new APIClient<Blob>(`${ENDPOINTS.REPORTS}${reportId}/download/`)
    return client.get({
      responseType: 'blob',
    })
  }

  /**
   * Get real-time metrics
   */
  getRealTimeMetrics = async (): Promise<{
    activeUsers: number
    currentOrders: number
    todayRevenue: number
    todayOrders: number
    systemHealth: 'healthy' | 'warning' | 'critical'
    lastUpdated: string
  }> => {
    const client = new APIClient<any>(`${ENDPOINTS.DASHBOARD}realtime/`)
    return client.get()
  }
}

export const AnalyticsService = new AnalyticsServiceClass()
