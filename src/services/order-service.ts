// Order management service
// Handles all order-related API operations for admin staff

import APIClient from './api-client'
import { Order, OrderFilters, OrderStatusUpdate, PaginatedResponse } from '../types/api-types'

class OrderService extends APIClient<Order> {
  constructor() {
    super('/api/staff/orders/orders/')
  }

  /**
   * Get paginated list of orders with filters
   */
  async getOrders(filters: OrderFilters = {}): Promise<PaginatedResponse<Order>> {
    const params = new URLSearchParams()

    // Add filter parameters
    if (filters.status) params.append('status', filters.status)
    if (filters.assigned_to) params.append('assigned_to', filters.assigned_to.toString())
    if (filters.customer) params.append('customer', filters.customer.toString())
    if (filters.date_from) params.append('date_from', filters.date_from)
    if (filters.date_to) params.append('date_to', filters.date_to)
    if (filters.search) params.append('search', filters.search)
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.page_size) params.append('page_size', filters.page_size.toString())
    if (filters.ordering) params.append('ordering', filters.ordering)

    return this.getAll({ params })
  }

  /**
   * Get single order by ID
   */
  async getOrder(orderId: number): Promise<Order> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/`
    return this.get()
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId: number, data: OrderStatusUpdate): Promise<Order> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/update_status/`
    return this.patch(data)
  }

  /**
   * Assign order to staff member
   */
  async assignOrder(orderId: number, staffId: number): Promise<Order> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/assign/`
    return this.patch({ assigned_to: staffId })
  }

  /**
   * Bulk update order statuses
   */
  async bulkUpdateStatus(orderIds: number[], status: string, notes?: string): Promise<{ updated: number; failed: number }> {
    this.endpoint = '/api/staff/orders/orders/bulk_update_status/'
    return this.post({
      order_ids: orderIds,
      status,
      notes
    })
  }

  /**
   * Get orders assigned to current user
   */
  async getMyAssignedOrders(filters: Omit<OrderFilters, 'assigned_to'> = {}): Promise<PaginatedResponse<Order>> {
    return this.getOrders({ ...filters, assigned_to: -1 }) // -1 indicates current user
  }

  /**
   * Export orders to CSV
   */
  async exportOrders(filters: OrderFilters = {}): Promise<Blob> {
    const params = new URLSearchParams()

    // Add filter parameters
    if (filters.status) params.append('status', filters.status)
    if (filters.assigned_to) params.append('assigned_to', filters.assigned_to.toString())
    if (filters.customer) params.append('customer', filters.customer.toString())
    if (filters.date_from) params.append('date_from', filters.date_from)
    if (filters.date_to) params.append('date_to', filters.date_to)
    if (filters.search) params.append('search', filters.search)

    this.endpoint = '/api/staff/orders/orders/export/'
    const response = await this.get({
      params,
      responseType: 'blob'
    })

    return response as unknown as Blob
  }

  /**
   * Generate order invoice
   */
  async generateInvoice(orderId: number): Promise<Blob> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/generate_invoice/`
    const response = await this.post({}, { responseType: 'blob' })
    return response as unknown as Blob
  }

  /**
   * Generate shipping label
   */
  async generateShippingLabel(orderId: number): Promise<Blob> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/generate_label/`
    const response = await this.post({}, { responseType: 'blob' })
    return response as unknown as Blob
  }

  /**
   * Add internal note to order
   */
  async addOrderNote(orderId: number, note: string, isInternal: boolean = true): Promise<void> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/add_note/`
    await this.post({
      note,
      is_internal: isInternal
    })
  }

  /**
   * Get order status history
   */
  async getOrderStatusHistory(orderId: number): Promise<any[]> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/status_history/`
    return this.get()
  }

  /**
   * Cancel order
   */
  async cancelOrder(orderId: number, reason: string): Promise<Order> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/cancel/`
    return this.post({ reason })
  }

  /**
   * Refund order
   */
  async refundOrder(orderId: number, amount?: number, reason?: string): Promise<any> {
    this.endpoint = `/api/staff/orders/orders/${orderId}/refund/`
    return this.post({
      amount,
      reason
    })
  }
}

// Export singleton instance
export const orderService = new OrderService()
export default orderService
