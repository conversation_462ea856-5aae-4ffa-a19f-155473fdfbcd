// Dashboard home route
// Main landing page for authenticated admin users

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../components/auth/AuthGuard'
import { DashboardPage } from '../pages/dashboard/DashboardPage'

export const Route = createFileRoute('/')({
  component: DashboardRoute,
})

function DashboardRoute() {
  return (
    <AuthGuard>
      <DashboardPage />
    </AuthGuard>
  )
}
