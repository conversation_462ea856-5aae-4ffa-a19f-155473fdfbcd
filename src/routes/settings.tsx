// Settings route with authentication guards
// Handles user settings and system configuration

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../components/auth/AuthGuard'
import { SettingsPage } from '../pages/settings/SettingsPage'

export const Route = createFileRoute('/settings')({
  component: SettingsRoute,
})

function SettingsRoute() {
  return (
    <AuthGuard>
      <SettingsPage />
    </AuthGuard>
  )
}
