// Categories management route
import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { CategoriesPage } from '../../pages/products/CategoriesPage/CategoriesPage'

export const Route = createFileRoute('/products/categories')({
  component: CategoriesRoute,
})

function CategoriesRoute() {
  return (
    <AuthGuard permission="staff.view_category">
      <CategoriesPage />
    </AuthGuard>
  )
}
