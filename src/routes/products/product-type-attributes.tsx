// Product Type-Attributes associations route
// Protected route for managing product type-attributes associations

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { ProductTypeAttributesPage } from '../../pages/products/ProductTypeAttributesPage'

export const Route = createFileRoute('/products/product-type-attributes')({
  component: ProductTypeAttributesRoute,
})

function ProductTypeAttributesRoute() {
  return (
    <AuthGuard permission="staff.view_producttypeattribute">
      <ProductTypeAttributesPage />
    </AuthGuard>
  )
}
