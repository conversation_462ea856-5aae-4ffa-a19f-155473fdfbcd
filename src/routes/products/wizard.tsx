// Product wizard route
// Multi-step product creation workflow

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { ProductWizardPage } from '../../pages/products/ProductWizardPage'

export const Route = createFileRoute('/products/wizard')({
  component: ProductWizardRoute,
})

function ProductWizardRoute() {
  return (
    <AuthGuard permission="staff.add_productproxy">
      <ProductWizardPage />
    </AuthGuard>
  )
}
