// Brand-Product Type associations route
// Protected route for managing brand-product type associations

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { BrandProductTypeAssociationPage } from '../../pages/products/BrandProductTypeAssociationPage'

export const Route = createFileRoute('/products/brand-product-types')({
  component: BrandProductTypesRoute,
})

function BrandProductTypesRoute() {
  return (
    <AuthGuard permission="staff.view_brandproducttype">
      <BrandProductTypeAssociationPage />
    </AuthGuard>
  )
}
