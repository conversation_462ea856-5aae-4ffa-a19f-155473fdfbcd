// Attributes management route
import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { AttributesPage } from '../../pages/products/AttributesPage'

export const Route = createFileRoute('/products/attributes')({
  component: AttributesRoute,
})

function AttributesRoute() {
  return (
    <AuthGuard permission="staff.view_attribute">
      <AttributesPage />
    </AuthGuard>
  )
}
