// Customers route with authentication and permission guards
// Handles customer management functionality

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../components/auth/AuthGuard'
import { CustomersListPage } from '../pages/customers/CustomersListPage'

export const Route = createFileRoute('/customers')({
  component: CustomersRoute,
})

function CustomersRoute() {
  return (
    <AuthGuard permission="staff.view_customerproxy">
      <CustomersListPage />
    </AuthGuard>
  )
}
