@import '../../../scss/variables.scss';
@import '../../../scss/mixins.scss';


.cart_item {
  margin: 0;
  padding: 1rem;
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 1rem;
  box-shadow: $box-shadow-1;
  width: 100%;
  height: fit-content;

  &:hover {
    box-shadow: $box-shadow-2;
  }
}

.cart_item__img {
  img {
    width: 100%;
  }
}

.cart_item__info {
  @include flexbox(flex-start, flex-start, column);
  gap: 0.5rem;

  span:first-child {
    a {
      color: $primary-dark;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .cart_item__title {
    font-size: 17.5;
    font-weight: bold;

    &:hover {
      color: $primary-blue;
    }
  }

  .cart_item__extra_data {
    @include flexbox(flex-start, center);
    gap: 0.5rem;
    font-size: 14.5px;
    color: $primary-lighter-text-color;

    p:first-child {
      font-weight: bold;
    }
  }
}

.cart_item__quantity {
  grid-column: 1 / 3;
  @include flexbox(flex-start, center, column);
  // background-color: #96eea2;
  // width: 100%;
  // margin: 0 auto;
  // column-gap: 1rem;

  div:first-child {
    // background-color: #978c8c;
    @include flexbox(flex-start, center);
    column-gap: .8rem;

    p {
      font-weight: bold;
      color: $primary-dark-text-color;
    }

    button {
      padding: 6px;
      border: 1.6px solid #fff;
      border-radius: 2px;
      transition: all .3s ease;

      &:hover {
        border: 1.6px solid $primary-blue;
        color: $primary-blue;
      }

      &:disabled:hover {
        border: 1.6px solid #fff;
        color: inherit;
        cursor: not-allowed;
      }
    }


    button:nth-child(5) {
      background-color: $error-red;
      border: 1.6px solid $error-red;
      transition: all .3s ease;

      i {
        font-weight: bold;
        color: #fff;
      }

      &:hover {
        background-color: darken($error-red, 10%);
        // border: 1.6px solid $error-red;


        i {
          color: #fff;
        }
      }
    }
  }

  p:last-child {
    margin: 10px 0;
    color: $primary-red;
    font-weight: bold;
    text-transform: none;
    // background-color: #c2a2a2;
  }
}

@media (width > $tablet) {
  .cart_item {
    grid-template-columns: 100px 2fr 1fr;
  }

  .cart_item__quantity {
    width: max-content;
    grid-column: 3 / 4;
    justify-content: center;
  }
}