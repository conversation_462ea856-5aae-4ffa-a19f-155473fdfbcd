@import '../../../scss/variables';
@import '../../../scss/mixins';


.cart__checkout {
  background-color: $sky-lighter-blue;
  padding: 1.5rem;
  @include flexbox(flex-start, flex-start, column);
  gap: 1.5rem;

  div {
    @include flexbox(space-between, center);
    width: 100%;
    font-size: 1.2rem;

    p:first-child {
      font-weight: bold;
      color: $primary-dark-text-color;
    }

    p:last-child {
      font-weight: bold;
      color: $primary-blue;
    }
  }

  div:nth-child(2) {
    p:first-child {
      @include flexbox(flex-start, center);
      column-gap: 2px;
    }
  }

  button {
    @include btn(#fff, $lighten-blue);
    padding: 1rem 0rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: .7px;
    transition: all .3s ease;

    &:hover {
      background-color: darken($lighten-blue, 10%);
      color: darken(#fff, 15%);
    }
  }
}