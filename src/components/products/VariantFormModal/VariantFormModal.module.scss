@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.form {
  @include flex-column;
  gap: $spacing-6;
  max-height: 80vh;
  overflow: hidden;
}

.formContent {
  @include flex-column;
  gap: $spacing-6;
  overflow-y: auto;
  padding-right: $spacing-2;
  margin-right: -$spacing-2;
}

.section {
  background: white;
  border: 1px solid $gray-200;
}

.sectionHeader {
  @include flex-start;
  gap: $spacing-3;
  margin-bottom: $spacing-4;
  padding-bottom: $spacing-3;
  border-bottom: 1px solid $gray-200;

  .sectionIcon {
    @include flex-center;
    width: $spacing-10;
    height: $spacing-10;
    background: $primary-50;
    color: $primary-600;
    border-radius: $border-radius;
    font-size: $font-size-base;
  }

  h3 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0;
  }

  p {
    font-size: $font-size-sm;
    color: $gray-600;
    margin: 0;
  }
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-4;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
}

.label {
  font-weight: $font-weight-medium;
  color: $gray-700;
  font-size: $font-size-sm;
}

.helpText {
  font-size: $font-size-xs;
  color: $gray-500;
  line-height: $line-height-normal;
}

.priceInput {
  position: relative;

  .priceIcon {
    position: absolute;
    left: $spacing-3;
    top: 50%;
    transform: translateY(-50%);
    color: $gray-400;
    font-size: $font-size-sm;
  }

  input {
    padding-left: $spacing-8;
  }
}

.discountInfo {
  @include flex-start;
  gap: $spacing-3;
  align-items: center;
  padding: $spacing-3;
  background: $success-25;
  border: 1px solid $success-200;
  border-radius: $border-radius;

  .discountText {
    font-size: $font-size-sm;
    color: $success-700;
  }
}

.switchGroup {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-4;
  padding: $spacing-4;
  background: $gray-50;
  border-radius: $border-radius;

  .switchInfo {
    @include flex-column;
    gap: $spacing-1;
    flex: 1;
  }
}

.attributesSection {
  .attributesGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacing-4;
    margin-top: $spacing-3;
  }

  .attributeGroup {
    @include flex-column;
    gap: $spacing-2;

    .attributeLabel {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $gray-700;
    }

    .attributeSelect {
      @include input-base;
    }
  }
}

.formActions {
  @include flex-end;
  gap: $spacing-3;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
  margin-top: auto;
  flex-shrink: 0;
}

// Responsive adjustments
@include mobile-only {
  .form {
    gap: $spacing-4;
  }

  .formContent {
    gap: $spacing-4;
  }

  .sectionHeader {
    gap: $spacing-2;

    .sectionIcon {
      width: $spacing-8;
      height: $spacing-8;
      font-size: $font-size-sm;
    }

    h3 {
      font-size: $font-size-sm;
    }

    p {
      font-size: $font-size-xs;
    }
  }

  .formActions {
    flex-direction: column-reverse;

    button {
      width: 100%;
    }
  }
}

// Loading states
.loading {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-12;
  color: $gray-600;

  p {
    font-size: $font-size-sm;
    margin: 0;
  }
}

// Error states
.errorState {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-8;
  color: $error-600;
  text-align: center;

  .errorIcon {
    font-size: $font-size-2xl;
    color: $error-400;
  }

  .errorTitle {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    margin: 0;
  }

  .errorMessage {
    font-size: $font-size-sm;
    color: $error-500;
    margin: 0;
  }
}
