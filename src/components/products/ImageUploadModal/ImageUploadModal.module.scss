@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
  max-height: 70vh;
  overflow-y: auto;
}

.variantSelection {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.label {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-700;
}

.variantSelect {
  padding: $spacing-3;
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  font-size: $font-size-base;
  background: $white;
  
  &:focus {
    outline: none;
    border-color: $primary-500;
    box-shadow: 0 0 0 1px $primary-500;
  }
}

.uploadSection {
  .dropzone {
    border: 2px dashed $gray-300;
    border-radius: $border-radius-lg;
    padding: $spacing-8;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background: $gray-50;
    
    &:hover,
    &.dragActive {
      border-color: $primary-400;
      background: $primary-50;
    }
  }

  .dropzoneContent {
    @include flex-column-center;
    gap: $spacing-3;

    .uploadIcon {
      font-size: 48px;
      color: $gray-400;
    }

    h4 {
      margin: 0;
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $gray-900;
    }

    p {
      margin: 0;
      font-size: $font-size-sm;
      color: $gray-600;
    }
  }
}

.imagesSection {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.sectionHeader {
  @include flex-between;
  align-items: center;

  h4 {
    margin: 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
  }
}

.imagesList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: $spacing-4;
  max-height: 400px;
  overflow-y: auto;
}

.imageCard {
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  overflow: hidden;
}

.imagePreview {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
  background: $gray-100;
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.imageOverlay {
  position: absolute;
  top: $spacing-2;
  right: $spacing-2;
}

.uploadedBadge {
  @include flex-center;
  gap: $spacing-1;
  padding: $spacing-1 $spacing-2;
  background: $green-500;
  color: $white;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
}

.uploadingBadge {
  padding: $spacing-1 $spacing-2;
  background: $yellow-500;
  color: $white;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
}

.removeButton {
  @include flex-center;
  padding: $spacing-1;
  min-width: auto;
  background: rgba($red-500, 0.9);
  
  &:hover {
    background: $red-600;
  }
}

.imageForm {
  padding: $spacing-2;
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: $spacing-0-5;
}

.error {
  font-size: $font-size-sm;
  color: $red-600;
  padding: $spacing-2;
  background: $red-50;
  border: 1px solid $red-200;
  border-radius: $border-radius-sm;
}

.uploadButton {
  @include flex-center;
  gap: $spacing-1;
  align-self: flex-start;
  font-size: $font-size-xs;
  padding: $spacing-1 $spacing-2;
}

.modalActions {
  @include flex-between;
  gap: $spacing-3;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;

  @include mobile-only {
    flex-direction: column-reverse;
  }
}

// Responsive adjustments
@include mobile-only {
  .container {
    max-height: 80vh;
  }

  .imagesList {
    grid-template-columns: repeat(2, 1fr);
    max-height: 300px;
  }

  .sectionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-2;
  }
}

// Loading states
.imageCard:has(.uploadingBadge) {
  opacity: 0.7;
}

.imageCard:has(.uploadedBadge) {
  border-color: $green-200;
  background: $green-25;
}
