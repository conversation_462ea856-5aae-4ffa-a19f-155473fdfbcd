// Image Upload Modal Component
// Modal for uploading multiple images to product variants

import React, { useState, useCallback } from 'react'
import { FiUpload, FiX, FiImage, FiTrash2, FiCheck } from 'react-icons/fi'
import { useDropzone } from 'react-dropzone'
import { useF<PERSON>, <PERSON> } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Modal } from '../../ui/Modal'
import { Button } from '../../ui/Button'
import { Input } from '../../ui/Input'
import { Card, CardBody } from '../../ui/Card'
import { useUploadProductImage } from '../../../hooks/products-hooks/use-product-images'
import type { ProductVariant } from '../../../types/api-types'
import styles from './ImageUploadModal.module.scss'

// Zod schema for image upload
const imageUploadSchema = z.object({
  alternative_text: z.string().min(1, 'Alternative text is required'),
})

type ImageUploadFormData = z.infer<typeof imageUploadSchema>

interface ImageFile {
  id: string
  file: File
  preview: string
  alternative_text: string
  uploading: boolean
  uploaded: boolean
  error?: string
}

interface ImageUploadModalProps {
  isOpen: boolean
  onClose: () => void
  variants: ProductVariant[]
  selectedVariantId?: number
}

export const ImageUploadModal: React.FC<ImageUploadModalProps> = ({
  isOpen,
  onClose,
  variants,
  selectedVariantId,
}) => {
  const [selectedVariant, setSelectedVariant] = useState<number>(selectedVariantId || variants[0]?.id || 0)
  const [imageFiles, setImageFiles] = useState<ImageFile[]>([])
  const [uploadingAll, setUploadingAll] = useState(false)

  const uploadImageMutation = useUploadProductImage()

  const form = useForm<ImageUploadFormData>({
    resolver: zodResolver(imageUploadSchema),
  })

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newImageFiles: ImageFile[] = acceptedFiles.map((file) => ({
      id: `${Date.now()}-${Math.random()}`,
      file,
      preview: URL.createObjectURL(file),
      alternative_text: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
      uploading: false,
      uploaded: false,
    }))

    setImageFiles(prev => [...prev, ...newImageFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: true,
  })

  // Remove image file
  const removeImageFile = (id: string) => {
    setImageFiles(prev => {
      const updated = prev.filter(img => img.id !== id)
      // Revoke object URL to prevent memory leaks
      const imageToRemove = prev.find(img => img.id === id)
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview)
      }
      return updated
    })
  }

  // Update alternative text for an image
  const updateAlternativeText = (id: string, text: string) => {
    setImageFiles(prev => prev.map(img => 
      img.id === id ? { ...img, alternative_text: text } : img
    ))
  }

  // Upload single image
  const uploadSingleImage = async (imageFile: ImageFile) => {
    if (!selectedVariant) return

    setImageFiles(prev => prev.map(img => 
      img.id === imageFile.id ? { ...img, uploading: true, error: undefined } : img
    ))

    try {
      await uploadImageMutation.mutateAsync({
        image: imageFile.file,
        alternative_text: imageFile.alternative_text,
        product_variant: selectedVariant,
      })

      setImageFiles(prev => prev.map(img => 
        img.id === imageFile.id ? { ...img, uploading: false, uploaded: true } : img
      ))
    } catch (error: any) {
      setImageFiles(prev => prev.map(img => 
        img.id === imageFile.id ? { 
          ...img, 
          uploading: false, 
          error: error.message || 'Upload failed' 
        } : img
      ))
    }
  }

  // Upload all images
  const uploadAllImages = async () => {
    if (!selectedVariant || imageFiles.length === 0) return

    setUploadingAll(true)

    const unuploadedImages = imageFiles.filter(img => !img.uploaded && !img.uploading)
    
    for (const imageFile of unuploadedImages) {
      await uploadSingleImage(imageFile)
    }

    setUploadingAll(false)
  }

  // Handle modal close
  const handleClose = () => {
    // Revoke all object URLs
    imageFiles.forEach(img => URL.revokeObjectURL(img.preview))
    setImageFiles([])
    setUploadingAll(false)
    onClose()
  }

  // Check if all images are uploaded
  const allImagesUploaded = imageFiles.length > 0 && imageFiles.every(img => img.uploaded)
  const hasUnuploadedImages = imageFiles.some(img => !img.uploaded && !img.uploading)

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Upload Product Images"
      size="lg"
    >
      <div className={styles.container}>
        {/* Variant Selection */}
        <div className={styles.variantSelection}>
          <label htmlFor="variant" className={styles.label}>
            Select Product Variant
          </label>
          <select
            id="variant"
            value={selectedVariant}
            onChange={(e) => setSelectedVariant(Number(e.target.value))}
            className={styles.variantSelect}
          >
            {variants.map(variant => (
              <option key={variant.id} value={variant.id}>
                {variant.sku} - ${variant.price}
              </option>
            ))}
          </select>
        </div>

        {/* File Upload Area */}
        <div className={styles.uploadSection}>
          <div
            {...getRootProps()}
            className={`${styles.dropzone} ${isDragActive ? styles.dragActive : ''}`}
          >
            <input {...getInputProps()} />
            <div className={styles.dropzoneContent}>
              <FiUpload className={styles.uploadIcon} />
              <h4>Drop images here or click to select</h4>
              <p>Support: JPEG, PNG, GIF, WebP</p>
            </div>
          </div>
        </div>

        {/* Image Preview and Upload */}
        {imageFiles.length > 0 && (
          <div className={styles.imagesSection}>
            <div className={styles.sectionHeader}>
              <h4>Images to Upload ({imageFiles.length})</h4>
              {hasUnuploadedImages && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={uploadAllImages}
                  disabled={uploadingAll || !selectedVariant}
                >
                  <FiUpload />
                  {uploadingAll ? 'Uploading...' : 'Upload All'}
                </Button>
              )}
            </div>

            <div className={styles.imagesList}>
              {imageFiles.map((imageFile) => (
                <Card key={imageFile.id} className={styles.imageCard}>
                  <CardBody>
                    <div className={styles.imagePreview}>
                      <img
                        src={imageFile.preview}
                        alt={imageFile.alternative_text}
                        className={styles.previewImage}
                      />
                      <div className={styles.imageOverlay}>
                        {imageFile.uploaded ? (
                          <div className={styles.uploadedBadge}>
                            <FiCheck />
                            Uploaded
                          </div>
                        ) : imageFile.uploading ? (
                          <div className={styles.uploadingBadge}>
                            Uploading...
                          </div>
                        ) : (
                          <Button
                            variant="danger"
                            size="sm"
                            onClick={() => removeImageFile(imageFile.id)}
                            className={styles.removeButton}
                          >
                            <FiTrash2 />
                          </Button>
                        )}
                      </div>
                    </div>

                    <div className={styles.imageForm}>
                      <div className={styles.formGroup}>
                        <label className={styles.label}>Alternative Text</label>
                        <Input
                          value={imageFile.alternative_text}
                          onChange={(e) => updateAlternativeText(imageFile.id, e.target.value)}
                          placeholder="Describe this image..."
                          disabled={imageFile.uploading || imageFile.uploaded}
                        />
                      </div>

                      {imageFile.error && (
                        <div className={styles.error}>
                          {imageFile.error}
                        </div>
                      )}

                      {!imageFile.uploaded && !imageFile.uploading && (
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => uploadSingleImage(imageFile)}
                          disabled={!imageFile.alternative_text.trim() || !selectedVariant}
                          className={styles.uploadButton}
                        >
                          <FiUpload />
                          Upload
                        </Button>
                      )}
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Modal Actions */}
        <div className={styles.modalActions}>
          <Button
            variant="secondary"
            onClick={handleClose}
          >
            {allImagesUploaded ? 'Done' : 'Cancel'}
          </Button>
          
          {allImagesUploaded && (
            <Button
              variant="primary"
              onClick={handleClose}
            >
              <FiCheck />
              Complete
            </Button>
          )}
        </div>
      </div>
    </Modal>
  )
}
