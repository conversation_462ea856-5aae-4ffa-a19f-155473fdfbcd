@use '../../../../scss/variables' as *;
@use '../../../../scss/mixins' as *;

.container {
  @include flex-column;
  gap: $spacing-6;
}

.header {
  .headerContent {
    h3 {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $gray-900;
      margin: 0 0 $spacing-2 0;
    }

    p {
      color: $gray-600;
      font-size: $font-size-sm;
      margin: 0;
      line-height: $line-height-relaxed;
    }
  }
}

.reviewSections {
  @include flex-column;
  gap: $spacing-4;
}

.reviewCard {
  background: white;
  border: 1px solid $gray-200;
}

.cardHeader {
  @include flex-between;
  align-items: center;
  margin-bottom: $spacing-4;
  padding-bottom: $spacing-3;
  border-bottom: 1px solid $gray-200;
}

.cardTitle {
  @include flex-start;
  align-items: center;
  gap: $spacing-3;

  svg {
    color: $primary-600;
    font-size: $font-size-base;
  }

  h4 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0;
  }
}

.cardContent {
  color: $gray-700;
}

.incomplete {
  color: $gray-500;
  font-style: italic;
  font-size: $font-size-sm;
}

.infoList {
  @include flex-column;
  gap: $spacing-3;
}

.infoItem {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-4;
  padding: $spacing-2;
  background: $gray-50;
  border-radius: $border-radius-sm;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-1;
  }
}

.infoLabel {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-600;
  min-width: 100px;
}

.infoValue {
  font-size: $font-size-sm;
  color: $gray-900;
  text-align: right;
  word-break: break-word;

  @include mobile-only {
    text-align: left;
  }
}

.attributesList {
  @include flex-column;
  gap: $spacing-2;
}

.attributeItem {
  @include flex-between;
  align-items: center;
  padding: $spacing-2;
  background: $gray-50;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
}

.variantsList {
  @include flex-column;
  gap: $spacing-4;
}

.variantsSummary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: $spacing-3;
}

.summaryItem {
  @include flex-between;
  align-items: center;
  padding: $spacing-2;
  background: $gray-50;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;

  span:first-child {
    color: $gray-600;
    font-weight: $font-weight-medium;
  }

  span:last-child {
    color: $gray-900;
    font-weight: $font-weight-semibold;
  }
}

.variantsPreview {
  @include flex-column;
  gap: $spacing-2;
}

.variantPreview {
  @include flex-between;
  align-items: center;
  padding: $spacing-3;
  background: $gray-50;
  border-radius: $border-radius;
  font-size: $font-size-sm;
}

.variantSku {
  font-weight: $font-weight-medium;
  color: $gray-900;
}

.variantPrice {
  font-weight: $font-weight-semibold;
  color: $primary-700;
}

.moreVariants {
  text-align: center;
  padding: $spacing-2;
  color: $gray-500;
  font-size: $font-size-sm;
  font-style: italic;
}

.submitSection {
  margin-top: $spacing-6;
}

.submitCard {
  background: $primary-25;
  border: 1px solid $primary-200;
}

.submitContent {
  @include flex-between;
  align-items: center;
  gap: $spacing-6;

  @include mobile-only {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-4;
  }
}

.submitInfo {
  flex: 1;

  h4 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $primary-900;
    margin: 0 0 $spacing-2 0;
  }

  p {
    color: $primary-700;
    font-size: $font-size-sm;
    margin: 0;
    line-height: $line-height-relaxed;
  }
}

.submitButton {
  @include flex-center;
  gap: $spacing-2;
  min-width: 180px;

  @include mobile-only {
    width: 100%;
  }
}
