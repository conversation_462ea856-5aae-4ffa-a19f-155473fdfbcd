@use '../../../../scss/variables' as *;
@use '../../../../scss/mixins' as *;

.container {
  @include flex-column;
  gap: $spacing-6;
}

.header {
  @include flex-between;
  gap: $spacing-4;
  
  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.headerContent {
  flex: 1;
  
  h3 {
    margin: 0 0 $spacing-2 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
  }
  
  p {
    margin: 0;
    color: $gray-600;
    font-size: $font-size-sm;
    line-height: 1.5;
  }
}

.selectedCard {
  background-color: $green-50;
  border: 1px solid $green-200;
}

.selectedCategory {
  @include flex-between;
  gap: $spacing-4;
}

.selectedIcon {
  @include flex-center;
  width: 40px;
  height: 40px;
  background-color: $green-100;
  border-radius: $border-radius;
  color: $green-600;
  flex-shrink: 0;
  
  svg {
    width: 20px;
    height: 20px;
  }
}

.selectedInfo {
  flex: 1;
  
  h4 {
    margin: 0 0 $spacing-1 0;
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $green-800;
  }
  
  p {
    margin: 0;
    font-size: $font-size-base;
    color: $green-700;
  }
}

.categoriesCard {
  flex: 1;
}

.categoriesHeader {
  padding-bottom: $spacing-4;
  border-bottom: 1px solid $gray-200;
  margin-bottom: $spacing-4;
  
  h4 {
    margin: 0 0 $spacing-1 0;
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $gray-900;
  }
  
  p {
    margin: 0;
    font-size: $font-size-sm;
    color: $gray-600;
  }
}

.categoriesTree {
  max-height: 400px;
  overflow-y: auto;
}

.categoryItem {
  @include flex-column;
}

.categoryRow {
  @include flex-start;
  gap: $spacing-2;
  padding: $spacing-2 $spacing-3;
  border-radius: $border-radius;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: $gray-50;
  }
  
  &.selected {
    background-color: $primary-50;
    border: 1px solid $primary-200;
  }
}

.expandButton {
  @include flex-center;
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  border-radius: $border-radius-sm;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: $gray-100;
  }
}

.expandIcon {
  width: 14px;
  height: 14px;
  color: $gray-500;
  transition: transform 0.2s ease-in-out;
  
  &.expanded {
    transform: rotate(90deg);
  }
}

.expandPlaceholder {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.categoryIcon {
  @include flex-center;
  width: 20px;
  height: 20px;
  color: $gray-500;
  flex-shrink: 0;
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.categoryButton {
  @include flex-between;
  flex: 1;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  padding: $spacing-1 0;
  gap: $spacing-3;
}

.categoryName {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-900;
}

.productCount {
  font-size: $font-size-xs;
  color: $gray-500;
  flex-shrink: 0;
}

.children {
  @include flex-column;
}

.emptyState {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-8;
  text-align: center;
  color: $gray-500;
  
  svg {
    width: 48px;
    height: 48px;
    color: $gray-400;
  }
  
  h4 {
    margin: 0;
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
  
  p {
    margin: 0;
    font-size: $font-size-sm;
    color: $gray-500;
  }
}

.loading {
  @include flex-column-center;
  gap: $spacing-4;
  padding: $spacing-8;
  
  p {
    margin: 0;
    color: $gray-600;
    font-size: $font-size-base;
  }
}

// Create modal styles
.createModal {
  @include flex-column;
  gap: $spacing-4;
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
  
  label {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
}

.select {
  @include input-base;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right $spacing-3 center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: $spacing-10;
  appearance: none;
}

.modalActions {
  @include flex-end;
  gap: $spacing-3;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}

// Responsive adjustments
@include mobile-only {
  .categoryRow {
    padding: $spacing-3;
  }
  
  .categoriesTree {
    max-height: 300px;
  }
  
  .modalActions {
    flex-direction: column;
    
    button {
      width: 100%;
      justify-content: center;
    }
  }
}
