// Category selection step in product wizard
// Allows selecting from hierarchical category tree

import React, { useState } from 'react'
import { FiPlus, FiChevronRight, FiFolder } from 'react-icons/fi'
import { useProductCategories, useCreateCategory } from '../../../../hooks/use-products'
import { Card } from '../../../ui/Card'
import { Button } from '../../../ui/Button'
import { Input } from '../../../ui/Input'
import { Modal } from '../../../ui/Modal'
import { LoadingSpinner } from '../../../ui/LoadingSpinner'
import { WizardData } from '../../../../pages/products/ProductWizardPage'
import { Category } from '../../../../types/api-types'
import styles from './CategoryStep.module.scss'

interface CategoryStepProps {
  data: WizardData
  onUpdate: (data: Partial<WizardData>) => void
}

export const CategoryStep: React.FC<CategoryStepProps> = ({ data, onUpdate }) => {
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set())
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [newCategoryName, setNewCategoryName] = useState('')
  const [selectedParentId, setSelectedParentId] = useState<number | null>(null)

  const { data: categories, isLoading, refetch } = useProductCategories()
  const createCategoryMutation = useCreateCategory()

  const toggleExpanded = (categoryId: number) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const handleCategorySelect = (category: Category) => {
    onUpdate({
      category: {
        id: category.id,
        name: category.title,
        parent: category.parent || undefined,
      }
    })
  }

  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) return

    try {
      await createCategoryMutation.mutateAsync({
        title: newCategoryName,
        parent: selectedParentId || undefined,
      })

      setIsCreateModalOpen(false)
      setNewCategoryName('')
      setSelectedParentId(null)
      refetch()
    } catch (error) {
      console.error('Failed to create category:', error)
    }
  }

  const renderCategoryTree = (categories: Category[], level = 0) => {
    return categories.map((category) => {
      const hasChildren = category.children && category.children.length > 0
      const isExpanded = expandedCategories.has(category.id)
      const isSelected = data.category?.id === category.id

      return (
        <div key={category.id} className={styles.categoryItem}>
          <div
            className={`${styles.categoryRow} ${isSelected ? styles.selected : ''}`}
            style={{ paddingLeft: `${level * 24 + 16}px` }}
          >
            {hasChildren ? (
              <button
                className={styles.expandButton}
                onClick={() => toggleExpanded(category.id)}
              >
                <FiChevronRight
                  className={`${styles.expandIcon} ${isExpanded ? styles.expanded : ''}`}
                />
              </button>
            ) : (
              <div className={styles.expandPlaceholder} />
            )}

            <div className={styles.categoryIcon}>
              {hasChildren ? (
                isExpanded ? <FiFolder /> : <FiFolder />
              ) : (
                <FiFolder />
              )}
            </div>

            <button
              className={styles.categoryButton}
              onClick={() => handleCategorySelect(category)}
            >
              <span className={styles.categoryName}>{category.title}</span>
              {category.products_count !== undefined && (
                <span className={styles.productCount}>
                  {category.products_count} products
                </span>
              )}
            </button>
          </div>

          {hasChildren && isExpanded && (
            <div className={styles.children}>
              {renderCategoryTree(category.children!, level + 1)}
            </div>
          )}
        </div>
      )
    })
  }

  if (isLoading) {
    return (
      <div className={styles.loading}>
        <LoadingSpinner size="lg" />
        <p>Loading categories...</p>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h3>Select Product Category</h3>
          <p>Choose the category that best describes your product. Categories help organize products and improve discoverability.</p>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsCreateModalOpen(true)}
        >
          <FiPlus />
          New Category
        </Button>
      </div>

      {data.category && (
        <Card className={styles.selectedCard}>
          <div className={styles.selectedCategory}>
            <div className={styles.selectedIcon}>
              <FiFolder />
            </div>
            <div className={styles.selectedInfo}>
              <h4>Selected Category</h4>
              <p>{data.category.name}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onUpdate({ category: undefined })}
            >
              Change
            </Button>
          </div>
        </Card>
      )}

      <Card className={styles.categoriesCard}>
        <div className={styles.categoriesHeader}>
          <h4>Available Categories</h4>
          <p>Click on a category to select it</p>
        </div>

        <div className={styles.categoriesTree}>
          {categories && categories.length > 0 ? (
            renderCategoryTree(categories)
          ) : (
            <div className={styles.emptyState}>
              <FiFolder />
              <h4>No categories found</h4>
              <p>Create your first category to get started</p>
              <Button
                variant="primary"
                onClick={() => setIsCreateModalOpen(true)}
              >
                <FiPlus />
                Create Category
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Create Category Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Category"
        size="md"
      >
        <div className={styles.createModal}>
          <div className={styles.formGroup}>
            <label htmlFor="categoryName">Category Name</label>
            <Input
              id="categoryName"
              type="text"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Enter category name"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="parentCategory">Parent Category (Optional)</label>
            <select
              id="parentCategory"
              className={styles.select}
              value={selectedParentId || ''}
              onChange={(e) => setSelectedParentId(e.target.value ? parseInt(e.target.value) : null)}
            >
              <option value="">No parent (top-level category)</option>
              {categories?.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.title}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.modalActions}>
            <Button
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleCreateCategory}
              disabled={!newCategoryName.trim() || createCategoryMutation.isPending}
            >
              {createCategoryMutation.isPending ? 'Creating...' : 'Create Category'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
