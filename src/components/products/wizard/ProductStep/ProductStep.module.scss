@use '../../../../scss/variables' as *;
@use '../../../../scss/mixins' as *;

.container {
  @include flex-column;
  gap: $spacing-6;
}

.header {
  .headerContent {
    h3 {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $gray-900;
      margin: 0 0 $spacing-2 0;
    }

    p {
      color: $gray-600;
      font-size: $font-size-sm;
      margin: 0;
      line-height: $line-height-relaxed;
    }
  }
}

.content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: $spacing-6;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.formCard,
.summaryCard {
  background: white;
  height: fit-content;
}

.cardHeader {
  @include flex-start;
  gap: $spacing-3;
  margin-bottom: $spacing-4;
  padding-bottom: $spacing-4;
  border-bottom: 1px solid $gray-200;
}

.cardIcon {
  @include flex-center;
  width: 40px;
  height: 40px;
  background: $primary-50;
  color: $primary-600;
  border-radius: $border-radius;
  font-size: $font-size-base;
}

.cardHeader h4 {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $gray-900;
  margin: 0;
}

.cardHeader p {
  color: $gray-600;
  font-size: $font-size-sm;
  margin: 0;
}

.form {
  @include flex-column;
  gap: $spacing-5;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-4;

  @include mobile-only {
    grid-template-columns: 1fr;
  }
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
}

.label {
  font-weight: $font-weight-medium;
  color: $gray-700;
  font-size: $font-size-sm;
}

.helpText {
  font-size: $font-size-xs;
  color: $gray-500;
  line-height: $line-height-normal;
}

.switchGroup {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-4;
  padding: $spacing-4;
  background: $gray-50;
  border-radius: $border-radius;
}

.switchInfo {
  @include flex-column;
  gap: $spacing-1;
  flex: 1;
}

.summary {
  @include flex-column;
  gap: $spacing-3;
}

.summaryItem {
  @include flex-between;
  align-items: center;
  padding: $spacing-3;
  background: $gray-50;
  border-radius: $border-radius;
}

.summaryLabel {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-700;
}

.summaryValue {
  font-size: $font-size-sm;
  color: $gray-900;
  text-align: right;

  &.active {
    color: $success-600;
    font-weight: $font-weight-medium;
  }

  &.inactive {
    color: $gray-500;
  }
}
