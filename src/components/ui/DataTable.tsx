// DataTable component for displaying tabular data
// Supports sorting, pagination, selection, and bulk actions

import React, { useState } from 'react';
import { 
  FiChevronUp, 
  FiChevronDown, 
  FiMoreHorizontal,
  FiLoader
} from 'react-icons/fi';
import { Button } from './Button';
import styles from './DataTable.module.scss';

interface Column<T> {
  key: keyof T | string;
  header: string;
  sortable?: boolean;
  render?: (value: any, row: T, index: number) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (key: string) => void;
  onRowClick?: (row: T, index: number) => void;
  selectedRows?: Set<string | number>;
  onRowSelect?: (rowId: string | number) => void;
  onSelectAll?: () => void;
  bulkActions?: React.ReactNode;
  emptyMessage?: string;
  rowKey?: keyof T;
  className?: string;
  stickyHeader?: boolean;
  maxHeight?: string;
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  sortBy,
  sortDirection,
  onSort,
  onRowClick,
  selectedRows,
  onRowSelect,
  onSelectAll,
  bulkActions,
  emptyMessage = 'No data available',
  rowKey = 'id' as keyof T,
  className,
  stickyHeader = false,
  maxHeight,
}: DataTableProps<T>) {
  const [hoveredRow, setHoveredRow] = useState<string | number | null>(null);

  const handleSort = (key: string) => {
    if (onSort) {
      onSort(key);
    }
  };

  const handleRowSelect = (rowId: string | number) => {
    if (onRowSelect) {
      onRowSelect(rowId);
    }
  };

  const isAllSelected = selectedRows && data.length > 0 && 
    data.every(row => selectedRows.has(row[rowKey]));

  const isIndeterminate = selectedRows && selectedRows.size > 0 && !isAllSelected;

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <FiLoader className={styles.loadingSpinner} />
        <span>Loading data...</span>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={styles.emptyState}>
        <p className={styles.emptyMessage}>{emptyMessage}</p>
      </div>
    );
  }

  const tableClasses = [
    styles.table,
    stickyHeader && styles.stickyHeader,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={styles.tableContainer}>
      {bulkActions && selectedRows && selectedRows.size > 0 && (
        <div className={styles.bulkActions}>
          <span className={styles.selectedCount}>
            {selectedRows.size} item{selectedRows.size !== 1 ? 's' : ''} selected
          </span>
          <div className={styles.bulkActionsContent}>
            {bulkActions}
          </div>
        </div>
      )}

      <div 
        className={styles.tableWrapper}
        style={{ maxHeight }}
      >
        <table className={tableClasses}>
          <thead className={styles.thead}>
            <tr>
              {onRowSelect && (
                <th className={styles.checkboxColumn}>
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    ref={(input) => {
                      if (input) input.indeterminate = !!isIndeterminate;
                    }}
                    onChange={onSelectAll}
                    className={styles.checkbox}
                    aria-label="Select all rows"
                  />
                </th>
              )}
              
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className={`${styles.th} ${column.sortable ? styles.sortable : ''}`}
                  style={{ 
                    width: column.width,
                    textAlign: column.align || 'left'
                  }}
                  onClick={column.sortable ? () => handleSort(String(column.key)) : undefined}
                >
                  <div className={styles.thContent}>
                    <span>{column.header}</span>
                    {column.sortable && sortBy === column.key && (
                      <span className={styles.sortIcon}>
                        {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              
              <th className={styles.actionsColumn}>
                <FiMoreHorizontal aria-label="Actions" />
              </th>
            </tr>
          </thead>
          
          <tbody className={styles.tbody}>
            {data.map((row, index) => {
              const rowId = row[rowKey];
              const isSelected = selectedRows?.has(rowId);
              const isHovered = hoveredRow === rowId;
              
              return (
                <tr
                  key={String(rowId)}
                  className={`
                    ${styles.tr} 
                    ${isSelected ? styles.selected : ''} 
                    ${isHovered ? styles.hovered : ''}
                    ${onRowClick ? styles.clickable : ''}
                  `}
                  onClick={onRowClick ? () => onRowClick(row, index) : undefined}
                  onMouseEnter={() => setHoveredRow(rowId)}
                  onMouseLeave={() => setHoveredRow(null)}
                >
                  {onRowSelect && (
                    <td className={styles.checkboxColumn}>
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleRowSelect(rowId)}
                        className={styles.checkbox}
                        onClick={(e) => e.stopPropagation()}
                        aria-label={`Select row ${index + 1}`}
                      />
                    </td>
                  )}
                  
                  {columns.map((column) => (
                    <td 
                      key={String(column.key)} 
                      className={styles.td}
                      style={{ textAlign: column.align || 'left' }}
                    >
                      {column.render 
                        ? column.render(row[column.key as keyof T], row, index)
                        : String(row[column.key as keyof T] || '')
                      }
                    </td>
                  ))}
                  
                  <td className={styles.actionsColumn}>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={(e) => e.stopPropagation()}
                      aria-label={`Actions for row ${index + 1}`}
                    >
                      <FiMoreHorizontal />
                    </Button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}
