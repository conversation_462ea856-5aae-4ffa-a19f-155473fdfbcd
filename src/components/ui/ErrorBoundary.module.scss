@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.errorBoundary {
  @include flexbox(center, center);
  min-height: 100vh;
  padding: $spacing-6;
  background: $gray-50;
}

.errorContainer {
  @include flexbox(center, center);
  flex-direction: column;
  text-align: center;
  max-width: 600px;
  width: 100%;
  padding: $spacing-8;
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-lg;
}

.errorIcon {
  @include flexbox(center, center);
  width: 80px;
  height: 80px;
  // background: $error-bg;
  color: $error;
  border-radius: $border-radius-full;
  margin-bottom: $spacing-6;
  animation: errorPulse 2s ease-in-out infinite;
}

@keyframes errorPulse {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba($error, 0.4);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba($error, 0);
  }
}

.errorContent {
  width: 100%;
}

.errorTitle {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-4;
}

.errorMessage {
  font-size: $font-size-2;
  color: $primary-lighter-text-color;
  line-height: 1.6;
  margin-bottom: $spacing-6;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.errorId {
  padding: $spacing-3;
  background: $gray-100;
  border-radius: $border-radius-md;
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  margin-bottom: $spacing-6;
  font-family: 'Courier New', monospace;
  word-break: break-all;

  strong {
    color: $primary-dark-text-color;
  }
}

.errorActions {
  @include flexbox(center, center);
  gap: $spacing-3;
  margin-bottom: $spacing-6;
  flex-wrap: wrap;

  @include mobile-only {
    flex-direction: column;
    width: 100%;

    >* {
      width: 100%;
    }
  }
}

.errorDetails {
  text-align: left;
  width: 100%;
  margin-top: $spacing-6;
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  overflow: hidden;

  summary {
    padding: $spacing-3 $spacing-4;
    background: $gray-100;
    cursor: pointer;
    font-weight: 600;
    color: $primary-dark-text-color;
    border-bottom: 1px solid $gray-200;
    transition: background-color $transition-fast;

    &:hover {
      background: $gray-200;
    }

    &::-webkit-details-marker {
      display: none;
    }

    &::before {
      content: '▶';
      display: inline-block;
      margin-right: $spacing-2;
      transition: transform $transition-fast;
    }
  }

  &[open] summary::before {
    transform: rotate(90deg);
  }
}

.errorStack {
  padding: $spacing-4;
  background: white;

  h4 {
    font-size: $font-size-2;
    font-weight: 600;
    color: $primary-dark-text-color;
    margin: $spacing-4 0 $spacing-2;

    &:first-child {
      margin-top: 0;
    }
  }

  pre {
    background: $gray-50;
    border: 1px solid $gray-200;
    border-radius: $border-radius-sm;
    padding: $spacing-3;
    font-size: $font-size-1;
    color: $primary-dark-text-color;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.4;
    font-family: 'Courier New', monospace;
  }
}

// Responsive adjustments
@include mobile-only {
  .errorBoundary {
    padding: $spacing-4;
    min-height: 100vh;
  }

  .errorContainer {
    padding: $spacing-6;
  }

  .errorIcon {
    width: 60px;
    height: 60px;
    margin-bottom: $spacing-4;

    svg {
      width: 32px;
      height: 32px;
    }
  }

  .errorDetails {
    margin-top: $spacing-4;
  }

  .errorStack {
    padding: $spacing-3;

    pre {
      font-size: 12px;
      padding: $spacing-2;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .errorBoundary {
    background: #111827;
  }

  .errorContainer {
    background: #1F2937;
    color: #F9FAFB;
  }

  .errorTitle {
    color: #F9FAFB;
  }

  .errorMessage {
    color: #D1D5DB;
  }

  .errorId {
    background: #374151;
    color: #D1D5DB;

    strong {
      color: #F9FAFB;
    }
  }

  .errorDetails {
    border-color: #374151;

    summary {
      background: #374151;
      color: #F9FAFB;
      border-color: #4B5563;

      &:hover {
        background: #4B5563;
      }
    }
  }

  .errorStack {
    background: #1F2937;

    h4 {
      color: #F9FAFB;
    }

    pre {
      background: #111827;
      border-color: #374151;
      color: #D1D5DB;
    }
  }
}