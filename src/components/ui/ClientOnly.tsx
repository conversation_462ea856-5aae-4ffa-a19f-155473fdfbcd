'use client'

// Client-only wrapper component to prevent SSR hydration issues
// Renders children only on the client side

import { useEffect, useState, ReactNode } from 'react'

interface ClientOnlyProps {
  children: ReactNode
  fallback?: ReactNode
}

export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Hook to check if component is mounted on client
export function useIsClient() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return isClient
}

// Hook for safe window access
export function useSafeWindow() {
  const [windowObj, setWindowObj] = useState<Window | null>(null)

  useEffect(() => {
    setWindowObj(window)
  }, [])

  return windowObj
}

// Hook for safe document access
export function useSafeDocument() {
  const [documentObj, setDocumentObj] = useState<Document | null>(null)

  useEffect(() => {
    setDocumentObj(document)
  }, [])

  return documentObj
}
