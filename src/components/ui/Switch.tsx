// Switch component for boolean inputs
// Accessible toggle switch with smooth animations

import React, { forwardRef } from 'react'
import styles from './Switch.module.scss'

interface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  size?: 'sm' | 'md' | 'lg'
  label?: string
}

export const Switch = forwardRef<HTMLInputElement, SwitchProps>(
  ({ className = '', size = 'md', label, id, ...props }, ref) => {
    const switchId = id || `switch-${Math.random().toString(36).substr(2, 9)}`

    return (
      <div className={`${styles.container} ${className}`}>
        <input
          ref={ref}
          type="checkbox"
          id={switchId}
          className={styles.input}
          {...props}
        />
        <label 
          htmlFor={switchId} 
          className={`${styles.switch} ${styles[size]}`}
        >
          <span className={styles.slider} />
        </label>
        {label && (
          <label htmlFor={switchId} className={styles.label}>
            {label}
          </label>
        )}
      </div>
    )
  }
)

Switch.displayName = 'Switch'
