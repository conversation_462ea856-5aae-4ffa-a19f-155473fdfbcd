import React from 'react'
import { FiMove, FiGripVertical } from 'react-icons/fi'
import { FaGripVertical } from 'react-icons/fa'
import styles from './DragIndicator.module.scss'

interface DragIndicatorProps {
  variant?: 'default' | 'compact' | 'minimal'
  className?: string
  tooltip?: string
}

export const DragIndicator: React.FC<DragIndicatorProps> = ({
  variant = 'default',
  className = '',
  tooltip = 'Drag to reorder'
}) => {
  const getIcon = () => {
    switch (variant) {
      case 'compact':
        return <FaGripVertical />
      case 'minimal':
        return <FiMove />
      default:
        return <FiMove />
    }
  }

  return (
    <div
      className={`${styles.dragIndicator} ${styles[variant]} ${className}`}
      title={tooltip}
      aria-label={tooltip}
    >
      {getIcon()}
      {variant === 'default' && (
        <span className={styles.dragText}>Drag</span>
      )}
    </div>
  )
}

export default DragIndicator
