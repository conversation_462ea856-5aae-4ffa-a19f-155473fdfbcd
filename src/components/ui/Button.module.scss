// Button component styles
// Implements design system with variants and states

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.button {
  @include button-base;

  // Size variants
  &.sm {
    @include button-size($spacing-2, $spacing-3, $font-size-sm);
  }

  &.md {
    @include button-size($spacing-3, $spacing-4, $font-size-base);
  }

  &.lg {
    @include button-size($spacing-4, $spacing-6, $font-size-lg);
  }

  // Color variants
  &.primary {
    background-color: $primary-600;
    // background-color: $primary-blue;
    color: white;

    &:hover:not(:disabled) {
      background-color: $primary-700;
    }

    &:active:not(:disabled) {
      background-color: $primary-800;
    }
  }

  &.secondary {
    background-color: $gray-600;
    color: white;

    &:hover:not(:disabled) {
      background-color: $gray-700;
    }

    &:active:not(:disabled) {
      background-color: $gray-800;
    }
  }

  &.outline {
    background-color: transparent;
    color: $primary-600;
    border: 1px solid $primary-600;

    &:hover:not(:disabled) {
      background-color: $primary-50;
      border-color: $primary-700;
      color: $primary-700;
    }

    &:active:not(:disabled) {
      background-color: $primary-100;
    }
  }

  &.ghost {
    background-color: transparent;
    color: $gray-600;

    &:hover:not(:disabled) {
      background-color: $gray-100;
      color: $gray-700;
    }

    &:active:not(:disabled) {
      background-color: $gray-200;
    }
  }

  &.danger {
    background-color: $error-600;
    color: white;

    &:hover:not(:disabled) {
      background-color: $error-700;
    }

    &:active:not(:disabled) {
      background-color: $error-800;
    }
  }

  &.success {
    background-color: $success-600;
    color: white;

    &:hover:not(:disabled) {
      background-color: $success-700;
    }

    &:active:not(:disabled) {
      background-color: $success-800;
    }
  }

  &.warning {
    background-color: $warning-600;
    color: white;

    &:hover:not(:disabled) {
      background-color: $warning-700;
    }

    &:active:not(:disabled) {
      background-color: $warning-800;
    }
  }

  // State modifiers
  &.fullWidth {
    width: 100%;
  }

  &.loading {
    position: relative;

    .spinner {
      margin-right: $spacing-2;
    }
  }

  // Focus styles
  &:focus-visible {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }
}

// Icon spacing
.content {
  display: flex;
  align-items: center;
  gap: $spacing-2;
}

.leftIcon,
.rightIcon {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.leftIcon {
  margin-right: $spacing-2;
}

.rightIcon {
  margin-left: $spacing-2;
}

// Loading spinner
.spinner {
  color: currentColor;
}
