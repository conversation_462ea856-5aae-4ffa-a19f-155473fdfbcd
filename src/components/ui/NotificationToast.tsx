'use client';

// Toast notification component with auto-dismiss and actions
// Supports different types and customizable behavior

import { useEffect, useState, memo, useRef } from 'react';
import { FiX, <PERSON>Check, FiAlertTriangle, FiInfo, FiAlertCircle } from 'react-icons/fi';
import { Button } from './Button';
import styles from './NotificationToast.module.scss';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationToastProps {
  notification: Notification;
  onClose: () => void;
}

const icons = {
  success: FiCheck,
  error: FiAlertCircle,
  warning: FiAlertTriangle,
  info: FiInfo,
};

export const NotificationToast = memo(function NotificationToast({ notification, onClose }: NotificationToastProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const Icon = icons[notification.type];

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsExiting(true);
    closeTimeoutRef.current = setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  const toastClasses = [
    styles.toast,
    styles[notification.type],
    isVisible && styles.visible,
    isExiting && styles.exiting,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={toastClasses} role="alert" aria-live="polite">
      <div className={styles.iconContainer}>
        <Icon className={styles.icon} size={20} />
      </div>
      
      <div className={styles.content}>
        <div className={styles.title}>{notification.title}</div>
        {notification.message && (
          <div className={styles.message}>{notification.message}</div>
        )}
        
        {notification.action && (
          <div className={styles.actions}>
            <Button
              variant="link"
              size="sm"
              onClick={notification.action.onClick}
              className={styles.actionButton}
            >
              {notification.action.label}
            </Button>
          </div>
        )}
      </div>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClose}
        className={styles.closeButton}
        aria-label="Close notification"
      >
        <FiX size={16} />
      </Button>
    </div>
  );
});
