@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.container {
  @include flex-column;
  gap: $spacing-1;
}

.textarea {
  @include input-base;
  min-height: 80px;
  font-family: inherit;
  line-height: $line-height-normal;

  &.sm {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-xs;
    min-height: 60px;
  }

  &.md {
    padding: $spacing-3 $spacing-4;
    font-size: $font-size-sm;
    min-height: 80px;
  }

  &.lg {
    padding: $spacing-4 $spacing-5;
    font-size: $font-size-base;
    min-height: 100px;
  }

  &.resize-none {
    resize: none;
  }

  &.resize-vertical {
    resize: vertical;
  }

  &.resize-horizontal {
    resize: horizontal;
  }

  &.resize-both {
    resize: both;
  }

  &.error {
    border-color: $error-500;
    
    &:focus {
      border-color: $error-500;
      box-shadow: 0 0 0 3px rgba($error-500, 0.1);
    }
  }

  &::placeholder {
    color: $gray-400;
  }
}

.errorMessage {
  font-size: $font-size-xs;
  color: $error-600;
  margin-top: $spacing-1;
}
