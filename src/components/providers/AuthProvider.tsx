'use client';

// Authentication provider that initializes auth state on app load
// <PERSON>les automatic authentication check and token refresh
// Aligned with React-TS implementation

import { useEffect } from 'react';
import { useAuthStore } from '../../stores/auth-store';
import { AuthService } from '../../services/api-client';

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  useEffect(() => {
    const initializeAuth = async () => {
      // Use store actions directly to avoid dependency issues
      const { setUser, setLoading, setError, setAuthenticated } = useAuthStore.getState();

      setLoading(true);
      setError(null);

      try {
        // Try to get current user (will use HTTP-only cookies)
        const user = await AuthService.getCurrentUser();
        setUser(user);
        setAuthenticated(true);
      } catch (error: any) {
        // If getCurrentUser fails, user is not authenticated
        // This is expected for unauthenticated users, so don't set error
        setUser(null);
        setAuthenticated(false);

        // Only log error if it's not a 401 (unauthorized)
        if (error?.status !== 401) {
          console.error('Auth initialization error:', error);
          setError('Failed to initialize authentication');
        }
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []); // Empty dependency array to run only once

  return <>{children}</>;
}
