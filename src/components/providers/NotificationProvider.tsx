'use client'

// Notification provider that renders toast notifications
// Uses the UI store for notification state management

import { memo, useCallback } from 'react'
import { useNotifications, useUIStore } from '../../stores/ui-store'
import { NotificationToast } from '../ui/NotificationToast'
import styles from './NotificationProvider.module.scss'

interface NotificationProviderProps {
  children: React.ReactNode
}

export const NotificationProvider = memo(function NotificationProvider({ children }: NotificationProviderProps) {
  const notifications = useNotifications()
  // Use direct store access for better performance
  const removeNotification = useUIStore(state => state.removeNotification)

  const handleClose = useCallback((id: string) => {
    removeNotification(id)
  }, [removeNotification])

  return (
    <>
      {children}

      {/* Notification Container */}
      <div className={styles.notificationContainer}>
        {notifications.map((notification) => (
          <NotificationToast
            key={notification.id}
            notification={notification}
            onClose={() => handleClose(notification.id)}
          />
        ))}
      </div>
    </>
  )
})
