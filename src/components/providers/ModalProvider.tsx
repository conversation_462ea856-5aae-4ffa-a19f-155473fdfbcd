'use client'

// Modal provider that renders modal components
// Uses the UI store for modal state management

import { memo, useCallback } from 'react'
import { useModals, useUIStore } from '../../stores/ui-store'
import { Modal } from '../ui/Modal'

interface ModalProviderProps {
  children: React.ReactNode
}

export const ModalProvider = memo(function ModalProvider({ children }: ModalProviderProps) {
  const modals = useModals()
  // Use direct store access for better performance
  const closeModal = useUIStore(state => state.closeModal)

  const handleClose = useCallback((id: string) => {
    closeModal(id)
  }, [closeModal])

  return (
    <>
      {children}

      {/* Render all active modals */}
      {modals.map((modal) => (
        <Modal
          key={modal.id}
          isOpen={true}
          onClose={() => handleClose(modal.id)}
          size={modal.size}
          closable={modal.closable}
        >
          <modal.component {...modal.props} />
        </Modal>
      ))}
    </>
  )
})
