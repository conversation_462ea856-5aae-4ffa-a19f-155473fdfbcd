// Header component styles
// Responsive header with breadcrumbs and user menu

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.header {
  @include flex-between;
  height: $header-height;
  padding: 0 $spacing-6;
  background: white;
  border-bottom: 1px solid $gray-200;
  box-shadow: $shadow-sm;
  position: sticky;
  top: 0;
  z-index: $z-sticky;

  @include mobile-only {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: $z-fixed;
  }
}

.left {
  @include flex-start;
  gap: $spacing-4;
  flex: 1;
  min-width: 0;
}

.mobileMenuButton {
  @include flex-center;
  width: $spacing-8;
  height: $spacing-8;
  background: none;
  border: none;
  border-radius: $border-radius;
  color: $gray-600;
  cursor: pointer;
  transition: $transition-colors;

  &:hover {
    background-color: $gray-100;
    color: $gray-900;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }

  svg {
    width: $spacing-5;
    height: $spacing-5;
  }

  @include desktop-only {
    display: none;
  }
}

.breadcrumbs {
  flex: 1;
  min-width: 0;
}

.breadcrumbList {
  @include flex-start;
  gap: $spacing-2;
  list-style: none;
  margin: 0;
  padding: 0;
  overflow-x: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;
}

.breadcrumbItem {
  @include flex-start;
  gap: $spacing-2;
  white-space: nowrap;
}

.breadcrumbLink {
  @include flex-start;
  gap: $spacing-1;
  color: $gray-600;
  text-decoration: none;
  font-size: $font-size-sm;
  padding: $spacing-1 $spacing-2;
  border-radius: $border-radius;
  transition: $transition-colors;

  &:hover {
    color: $gray-900;
    background-color: $gray-100;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }

  svg {
    width: $spacing-4;
    height: $spacing-4;
  }
}

.breadcrumbText {
  @include mobile-only {
    display: none;
  }
}

.breadcrumbSeparator {
  color: $gray-400;
  width: $spacing-3;
  height: $spacing-3;
  flex-shrink: 0;
}

.breadcrumbCurrent {
  color: $gray-900;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  padding: $spacing-1 $spacing-2;
}

.right {
  @include flex-start;
  gap: $spacing-3;
}

.notificationButton {
  @include flex-center;
  position: relative;
  width: $spacing-8;
  height: $spacing-8;
  background: none;
  border: none;
  border-radius: $border-radius;
  color: $gray-600;
  cursor: pointer;
  transition: $transition-colors;

  &:hover {
    background-color: $gray-100;
    color: $gray-900;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }

  svg {
    width: $spacing-5;
    height: $spacing-5;
  }
}

.notificationBadge {
  position: absolute;
  top: $spacing-1;
  right: $spacing-1;
  background-color: $error-500;
  color: white;
  font-size: $font-size-xs;
  font-weight: $font-weight-semibold;
  padding: $spacing-0-5 $spacing-1;
  border-radius: $border-radius-full;
  min-width: $spacing-4;
  height: $spacing-4;
  @include flex-center;
  line-height: 1;
}

.userMenu {
  position: relative;
}

.userMenuButton {
  @include flex-start;
  gap: $spacing-3;
  background: none;
  border: none;
  padding: $spacing-2;
  border-radius: $border-radius;
  cursor: pointer;
  transition: $transition-colors;

  &:hover {
    background-color: $gray-100;
  }

  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }
}

.userAvatar {
  @include flex-center;
  width: $spacing-8;
  height: $spacing-8;
  background: linear-gradient(135deg, $primary-500, $primary-600);
  color: white;
  border-radius: $border-radius-full;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  flex-shrink: 0;
}

.userInfo {
  @include flex-column;
  gap: $spacing-0-5;
  text-align: left;
  min-width: 0;

  @include mobile-only {
    display: none;
  }
}

.userName {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-900;
  @include truncate;
}

.userRole {
  font-size: $font-size-xs;
  color: $gray-500;
  @include truncate;
}

.chevron {
  width: $spacing-4;
  height: $spacing-4;
  color: $gray-400;
  transition: $transition-transform;

  .userMenuButton[aria-expanded="true"] & {
    transform: rotate(180deg);
  }

  @include mobile-only {
    display: none;
  }
}

.userMenuDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: $spacing-2;
  width: 280px;
  background: white;
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
  z-index: $z-dropdown;
  overflow: hidden;
}

.userMenuHeader {
  @include flex-start;
  gap: $spacing-3;
  padding: $spacing-4;
  background-color: $gray-50;
}

.userMenuAvatar {
  @include flex-center;
  width: $spacing-10;
  height: $spacing-10;
  background: linear-gradient(135deg, $primary-500, $primary-600);
  color: white;
  border-radius: $border-radius-full;
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  flex-shrink: 0;
}

.userMenuName {
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  color: $gray-900;
  margin-bottom: $spacing-0-5;
}

.userMenuEmail {
  font-size: $font-size-xs;
  color: $gray-500;
}

.userMenuDivider {
  height: 1px;
  background-color: $gray-200;
}

.userMenuItems {
  padding: $spacing-2 0;
}

.userMenuItem {
  @include flex-start;
  gap: $spacing-3;
  width: 100%;
  padding: $spacing-3 $spacing-4;
  background: none;
  border: none;
  color: $gray-700;
  text-decoration: none;
  font-size: $font-size-sm;
  cursor: pointer;
  transition: $transition-colors;

  &:hover {
    background-color: $gray-50;
    color: $gray-900;
  }

  &:focus {
    outline: none;
    background-color: $primary-50;
    color: $primary-700;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: $spacing-4;
    height: $spacing-4;
    color: $gray-500;
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: $z-dropdown - 1;
}

// Responsive adjustments
@include responsive(sm) {
  .header {
    padding: 0 $spacing-8;
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .chevron {
    transition: none;
  }
}