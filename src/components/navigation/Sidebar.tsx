// Sidebar navigation component
// Role-based navigation with collapsible design

import React, { useState } from 'react'
import { Link, useLocation } from '@tanstack/react-router'
import {
  FiHome,
  FiShoppingCart,
  FiPackage,
  FiUsers,
  FiBarChart,
  FiSettings,
  FiMenu,
  FiX,
  FiChevronDown,
  FiChevronRight,
  FiTag,
  FiGrid,
  FiLayers,
  FiPlus,
  FiList,
  FiEdit,
  FiTool
} from 'react-icons/fi'
import { useAuth } from '../../hooks/use-auth'
import { useSidebar } from '../../stores/ui-store'
import styles from './Sidebar.module.scss'

interface NavigationItem {
  id: string
  label: string
  path: string
  icon: React.ComponentType
  permission?: string
  group?: string
  children?: NavigationItem[]
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/',
    icon: FiHome,
  },
  {
    id: 'orders',
    label: 'Orders',
    path: '/orders',
    icon: FiShoppingCart,
    permission: 'staff.view_orderproxy',
    children: [
      {
        id: 'orders-list',
        label: 'All Orders',
        path: '/orders',
        icon: FiList,
        permission: 'staff.view_orderproxy',
      },
      {
        id: 'orders-create',
        label: 'Create Order',
        path: '/orders/new',
        icon: FiPlus,
        permission: 'staff.add_orderproxy',
      },
    ],
  },
  {
    id: 'products',
    label: 'Products',
    path: '/products',
    icon: FiPackage,
    permission: 'staff.view_productproxy',
    children: [
      {
        id: 'products-list',
        label: 'All Products',
        path: '/products',
        icon: FiList,
        permission: 'staff.view_productproxy',
      },
      {
        id: 'products-wizard',
        label: 'Add Product (Wizard)',
        path: '/products/wizard',
        icon: FiPlus,
        permission: 'staff.add_productproxy',
      },
      {
        id: 'products-categories',
        label: '1-Categories',
        path: '/products/categories',
        icon: FiGrid,
        permission: 'staff.view_category',
      },
      {
        id: 'products-types',
        label: '2-Product Types',
        path: '/products/types',
        icon: FiLayers,
        permission: 'staff.view_producttype',
      },
      {
        id: 'products-brands',
        label: '3-Brands',
        path: '/products/brands',
        icon: FiTag,
        permission: 'staff.view_brand',
      },
      {
        id: 'products-brand-product-types',
        label: '4-Product Type Brands',
        path: '/products/brand-product-types',
        icon: FiSettings,
        permission: 'staff.view_brandproducttype',
      },
      {
        id: 'products-attributes',
        label: '5-Attributes',
        path: '/products/attributes',
        icon: FiTool,
        permission: 'staff.view_attribute',
      },
      {
        id: 'products-product-type-attributes',
        label: '6-Product Type Attributes',
        path: '/products/product-type-attributes',
        icon: FiSettings,
        permission: 'staff.view_producttypeattribute',
      },
      {
        id: 'products-attribute-values',
        label: '7-Attribute Values',
        path: '/products/attribute-values',
        icon: FiTool,
        permission: 'staff.view_attributevalue',
      },
      {
        id: 'products-quick-add',
        label: '8-Add Product',
        path: '/products/new',
        icon: FiEdit,
        permission: 'staff.add_productproxy',
      },
    ],
  },
  {
    id: 'customers',
    label: 'Customers',
    path: '/customers',
    icon: FiUsers,
    permission: 'staff.view_customerproxy',
    children: [
      {
        id: 'customers-list',
        label: 'All Customers',
        path: '/customers',
        icon: FiList,
        permission: 'staff.view_customerproxy',
      },
      {
        id: 'customers-create',
        label: 'Add Customer',
        path: '/customers/new',
        icon: FiPlus,
        permission: 'staff.add_customerproxy',
      },
    ],
  },
  {
    id: 'analytics',
    label: 'Analytics',
    path: '/analytics',
    icon: FiBarChart,
    permission: 'staff.view_analytics',
    children: [
      {
        id: 'analytics-dashboard',
        label: 'Dashboard',
        path: '/analytics',
        icon: FiBarChart,
        permission: 'staff.view_analytics',
      },
      {
        id: 'analytics-reports',
        label: 'Reports',
        path: '/analytics/reports',
        icon: FiList,
        permission: 'staff.view_analytics',
      },
    ],
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: FiSettings,
    children: [
      {
        id: 'settings-general',
        label: 'General',
        path: '/settings',
        icon: FiSettings,
      },
      {
        id: 'settings-profile',
        label: 'Profile',
        path: '/settings/profile',
        icon: FiUsers,
      },
    ],
  },
]

export const Sidebar: React.FC = () => {
  const location = useLocation()
  const { user, checkPermission, hasGroup } = useAuth()
  const {
    sidebarCollapsed,
    sidebarMobileOpen,
    toggleSidebar,
    toggleMobileSidebar
  } = useSidebar()
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  const isItemVisible = (item: NavigationItem): boolean => {
    // Superuser can see everything
    if (user?.is_superuser) return true

    // Check group membership
    if (item.group && !hasGroup(item.group)) return false

    // Check permission
    if (item.permission && !checkPermission(item.permission)) return false

    return true
  }

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedItems(newExpanded)
  }

  const isItemActive = (path: string): boolean => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  const hasActiveChild = (item: NavigationItem): boolean => {
    if (!item.children) return false
    return item.children.some(child => isItemActive(child.path))
  }

  const renderNavigationItem = (item: NavigationItem) => {
    if (!isItemVisible(item)) return null

    const isActive = isItemActive(item.path)
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.has(item.id)
    const hasActiveChildren = hasActiveChild(item)

    // Auto-expand if has active children
    React.useEffect(() => {
      if (hasActiveChildren && !isExpanded) {
        setExpandedItems(prev => new Set([...prev, item.id]))
      }
    }, [hasActiveChildren, isExpanded, item.id])

    if (hasChildren) {
      return (
        <li key={item.id} className={styles.navigationItem}>
          <div className={`${styles.navigationParent} ${hasActiveChildren ? styles.hasActiveChild : ''}`}>
            <button
              className={`${styles.navigationLink} ${hasActiveChildren ? styles.active : ''}`}
              onClick={() => toggleExpanded(item.id)}
              title={sidebarCollapsed ? item.label : undefined}
            >
              <item.icon className={styles.icon} />
              {!sidebarCollapsed && (
                <>
                  <span className={styles.label}>{item.label}</span>
                  <span className={styles.expandIcon}>
                    {isExpanded ? <FiChevronDown /> : <FiChevronRight />}
                  </span>
                </>
              )}
            </button>

            {!sidebarCollapsed && isExpanded && (
              <ul className={styles.subMenu}>
                {item.children.map(child => renderSubNavigationItem(child))}
              </ul>
            )}
          </div>
        </li>
      )
    }

    return (
      <li key={item.id} className={styles.navigationItem}>
        <Link
          to={item.path}
          className={`${styles.navigationLink} ${isActive ? styles.active : ''}`}
          title={sidebarCollapsed ? item.label : undefined}
        >
          <item.icon className={styles.icon} />
          {!sidebarCollapsed && (
            <span className={styles.label}>{item.label}</span>
          )}
        </Link>
      </li>
    )
  }

  const renderSubNavigationItem = (item: NavigationItem) => {
    if (!isItemVisible(item)) return null

    const isActive = isItemActive(item.path)

    return (
      <li key={item.id} className={styles.subNavigationItem}>
        <Link
          to={item.path}
          className={`${styles.subNavigationLink} ${isActive ? styles.active : ''}`}
        >
          <item.icon className={styles.subIcon} />
          <span className={styles.subLabel}>{item.label}</span>
        </Link>
      </li>
    )
  }

  return (
    <>
      {/* Mobile Overlay */}
      {sidebarMobileOpen && (
        <div
          className={styles.mobileOverlay}
          onClick={toggleMobileSidebar}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        ${styles.sidebar} 
        ${sidebarCollapsed ? styles.collapsed : ''} 
        ${sidebarMobileOpen ? styles.mobileOpen : ''}
      `}>
        <div className={styles.header}>
          <div className={styles.logo}>
            {sidebarCollapsed ? (
              <span className={styles.logoCollapsed}>AA</span>
            ) : (
              <span className={styles.logoFull}>Admin Arena</span>
            )}
          </div>

          <button
            className={styles.toggleButton}
            onClick={toggleSidebar}
            aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <FiMenu />
          </button>

          <button
            className={styles.mobileCloseButton}
            onClick={toggleMobileSidebar}
            aria-label="Close sidebar"
          >
            <FiX />
          </button>
        </div>

        <nav className={styles.navigation}>
          <ul className={styles.navigationList}>
            {navigationItems.map(renderNavigationItem)}
          </ul>
        </nav>

        <div className={styles.footer}>
          {!sidebarCollapsed && user && (
            <div className={styles.userInfo}>
              <div className={styles.userAvatar}>
                {user.email.charAt(0).toUpperCase()}
              </div>
              <div className={styles.userDetails}>
                <span className={styles.userName}>
                  {user.staff_profile?.full_name || user.email}
                </span>
                <span className={styles.userRole}>
                  {user.staff_profile?.position_title || 'Admin'}
                </span>
              </div>
            </div>
          )}
        </div>
      </aside>
    </>
  )
}
