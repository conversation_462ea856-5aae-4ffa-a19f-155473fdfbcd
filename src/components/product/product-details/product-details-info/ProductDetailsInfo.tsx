import { <PERSON><PERSON>inus, FiPlus } from "react-icons/fi"
import { useNavigate } from 'react-router-dom'
import { useEffect, useMemo, useState } from 'react'
import loading_svg from '../../../../assets/loading_svg_white.svg'
import useAddToCart from '../../../../react-query/hooks/cart-hooks/useAddToCart'
import { ProductShape, ProductVariant } from '../../../../types/product-types'
import cartStore from '../../../../zustand/cartStore'
import { Rating } from '../../../rating/Rating'
import Alert from "../../../utils/alert/Alert"
import WishlistButton from "../../../wishlist/wishlist-button/WishlistButton"
import SelectableAttributeValues from '../../selectable-attr-values/SelectableAttributeValues'
import { getColorSelectionImage, getVariantColorValue, getFallbackColor } from '../../../../utils/color-image-utils'
import styles from './ProductDetailsInfo.module.scss'


interface Props {
  product: ProductShape,
  handleVariantClick: (variant: ProductVariant) => void,
  handleQtyChange: (newQty: number) => void,
  qty: number,
  selectedImage: string | null
}

const ProductDetailsInfo = ({ product, handleVariantClick, handleQtyChange, qty }: Props) => {
  const navigate = useNavigate()
  const { selectedVariant, setExtraData } = cartStore()
  const { handleAddToCart, isPending, error } = useAddToCart(product, qty)

  // State to track selected attribute values
  const [selectedAttributes, setSelectedAttributes] = useState<Record<string, string>>({})
  // Track if user has interacted with variant/attribute selection
  const [hasUserInteracted, setHasUserInteracted] = useState(false)

  // Compute required attributes
  const requiredAttributes = useMemo(() => {
    // If there are no option selectors and no selectable attribute values, there are no required attributes
    if ((!product?.option_selectors || product.option_selectors.length === 0) && (!product?.selectable_attribute_values || product.selectable_attribute_values.length === 0)) {
      return []
    }
    if (product?.option_selectors && product.option_selectors.length > 0) {
      return product.option_selectors.map(sel => sel.attribute_title)
    } else if (product?.selectable_attribute_values && product.selectable_attribute_values.length > 0) {
      // fallback: get unique attribute titles from selectable_attribute_values
      return Array.from(new Set(product.selectable_attribute_values.map(val => val.attribute_value.attribute.title)))
    }
    return []
  }, [product])

  // Only set defaults on initial load (not after user interaction)
  useEffect(() => {
    if (!hasUserInteracted && selectedVariant && requiredAttributes.length > 0) {
      const initial: Record<string, string> = {}
      selectedVariant.attribute_value.forEach(attr => {
        initial[attr.attribute.title] = attr.attribute_value
      })
      setSelectedAttributes(initial)
    }
    // eslint-disable-next-line
  }, [selectedVariant, requiredAttributes, hasUserInteracted])

  const missingAttributes = requiredAttributes.filter(attr => !selectedAttributes[attr])
  const isAddToCartDisabled = isPending || selectedVariant?.stock_qty === 0 || (requiredAttributes.length > 0 && missingAttributes.length > 0)

  // Calculate discount percentage
  const calculateDiscount = (price?: number) => {
    if (!price) return 0
    const originalPrice = price + price * 0.25
    return Math.round(((originalPrice - price) / originalPrice) * 100)
  }

  // Format price with discount
  function priceWithTrickyDiscount(price?: number) {
    if (price)
      return (price + price * 0.25).toFixed(2)
    return "0.00"
  }

  // Function to render stock message based on stock_qty
  const renderStockMessage = (stockQty: number) => {
    return (
      stockQty >= 15 ? "In Stock" : stockQty === 0 ? 'Out of Stock' : `Only ${stockQty} left in stock - order soon`
    )
  }

  // Get the primary attribute title (the one used for price_label)
  const primaryAttributeTitle = product?.product_variant?.[0]?.price_label_attr_title || "Options"

  // Group variants by primary attribute (e.g., Size) and sort by order
  const groupedVariants = useMemo(() => {
    if (!product?.product_variant || product.product_variant.length === 0) {
      return []
    }

    // First, sort all variants by their order field
    const sortedVariants = [...product.product_variant].sort((a, b) => a.order - b.order)

    // Create a map to group variants by their primary attribute value
    const groups: Record<string, {
      primaryValue: string,
      price: number,
      order: number,
      variants: ProductVariant[]
    }> = {}

    // Group variants by their primary attribute value
    sortedVariants.forEach(variant => {
      const primaryValue = variant.price_label

      // If this primary value hasn't been seen before, create a new group
      if (!groups[primaryValue]) {
        groups[primaryValue] = {
          primaryValue,
          price: variant.price,
          order: variant.order, // Use the order from the first variant in this group
          variants: []
        }
      }

      // Add this variant to its group
      groups[primaryValue].variants.push(variant)
    })

    // Convert the map to an array and sort by the order field
    return Object.values(groups).sort((a, b) => a.order - b.order)
  }, [product])

  // Get secondary attributes (attributes other than the primary one)
  const secondaryAttributes = useMemo(() => {
    if (!product?.option_selectors) {
      return []
    }

    return product.option_selectors.filter(selector =>
      selector.attribute_title.toLowerCase() !== primaryAttributeTitle.toLowerCase()
    )
  }, [product, primaryAttributeTitle])

  // Helper function to get all available attribute values for a given attribute
  // based on the current selections, ordered by variant order
  const getAvailableAttributeValues = (attributeTitle: string) => {
    if (!product?.product_variant) return []

    // Create a temporary set of attributes that includes all current selections
    // but excludes the attribute we're checking
    const currentSelections = { ...selectedAttributes }
    delete currentSelections[attributeTitle]

    // Sort variants by order first
    const sortedVariants = [...product.product_variant].sort((a, b) => a.order - b.order)

    // Find variants that match all currently selected attributes
    const matchingVariants = sortedVariants.filter(variant => {
      // If no attributes are selected, all variants match
      if (Object.keys(currentSelections).length === 0) return true

      // Check if this variant matches all currently selected attributes
      return Object.entries(currentSelections).every(([attrTitle, attrValue]) => {
        return variant.attribute_value.some(attr =>
          attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
          attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
        )
      })
    })

    // Extract all unique values for the given attribute from matching variants
    // while preserving the order based on variant order
    const availableValues: string[] = []
    const seenValues = new Set<string>()

    matchingVariants.forEach(variant => {
      variant.attribute_value.forEach(attr => {
        if (attr.attribute.title.toLowerCase() === attributeTitle.toLowerCase()) {
          const lowerValue = attr.attribute_value.toLowerCase()
          if (!seenValues.has(lowerValue)) {
            seenValues.add(lowerValue)
            availableValues.push(lowerValue)
          }
        }
      })
    })

    return availableValues
  }

  // Initialize selected attributes based on the selected variant
  useEffect(() => {
    if (selectedVariant && Object.keys(selectedAttributes).length === 0) {
      // Initialize selected attributes based on the selected variant
      const initialAttributes: Record<string, string> = {}
      selectedVariant.attribute_value.forEach(attr => {
        initialAttributes[attr.attribute.title] = attr.attribute_value
      })
      setSelectedAttributes(initialAttributes)
    }
  }, [selectedVariant, selectedAttributes])

  // Handle primary attribute selection (e.g., Size) with toggle functionality
  const handlePrimarySelection = (value: string) => {
    setHasUserInteracted(true)
    const isCurrentlySelected = selectedAttributes[primaryAttributeTitle] === value

    // Find the default variant (lowest order)
    const sortedVariants = [...(product?.product_variant || [])].sort((a, b) => a.order - b.order)
    const defaultVariant = sortedVariants[0]
    const isDefaultVariant = defaultVariant && defaultVariant.price_label === value

    if (isCurrentlySelected) {
      // If already selected, deselect it (reset to initial state)
      setSelectedAttributes({})
      setExtraData({})
      if (sortedVariants.length > 0) {
        handleVariantClick(sortedVariants[0])
      }
    } else if (isDefaultVariant) {
      // If user selects the default variant, reset to its default attribute values
      const initial: Record<string, string> = {}
      defaultVariant.attribute_value.forEach(attr => {
        initial[attr.attribute.title] = attr.attribute_value
      })
      setSelectedAttributes(initial)
      setExtraData(initial)
      handleVariantClick(defaultVariant)
    } else {
      // Only set the primary attribute, clear all others
      setSelectedAttributes({ [primaryAttributeTitle]: value })
      setExtraData({ [primaryAttributeTitle]: value })
      // Select a variant with this primary value
      const variantsWithPrimaryValue = product?.product_variant?.filter(v => v.price_label === value) || []
      if (variantsWithPrimaryValue.length > 0) {
        const sortedPrimaryVariants = variantsWithPrimaryValue.sort((a, b) => a.order - b.order)
        handleVariantClick(sortedPrimaryVariants[0])
      }
    }
  }

  // Handle attribute selection with cascading filtering and toggle functionality
  const handleAttributeSelection = (attributeTitle: string, _valueId: number, valueText: string) => {
    setHasUserInteracted(true)
    // Check if this attribute value is already selected (toggle functionality)
    const isCurrentlySelected = selectedAttributes[attributeTitle] === valueText

    if (isCurrentlySelected) {
      // If already selected, deselect it
      const newAttributes = { ...selectedAttributes }
      delete newAttributes[attributeTitle]

      // Update the selected attributes
      setSelectedAttributes(newAttributes)

      // Update extra data in cart store
      const newExtraData = { ...cartStore.getState().cartItem.extra_data }
      delete newExtraData[attributeTitle]
      setExtraData(newExtraData)

      // Find a matching variant with the remaining selected attributes
      if (Object.keys(newAttributes).length > 0) {
        const matchingVariants = product?.product_variant?.filter(variant => {
          return Object.entries(newAttributes).every(([attrTitle, attrValue]) => {
            return variant.attribute_value.some(attr =>
              attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
              attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
            )
          })
        }) || []

        if (matchingVariants.length > 0) {
          // Sort by order and select the first one
          const sortedVariants = matchingVariants.sort((a, b) => a.order - b.order)
          handleVariantClick(sortedVariants[0])
        }
      } else {
        // If no attributes are selected, go back to the first variant
        const sortedVariants = [...(product?.product_variant || [])].sort((a, b) => a.order - b.order)
        if (sortedVariants.length > 0) {
          handleVariantClick(sortedVariants[0])
        }
      }
    } else {
      // If not selected, select it (original logic)
      // Make sure we have the primary attribute value if this is a secondary attribute
      if (attributeTitle.toLowerCase() !== primaryAttributeTitle.toLowerCase()) {
        const primaryValue = selectedAttributes[primaryAttributeTitle]
        if (!primaryValue) return
      }

      // Create new attributes object with the new selection
      const newAttributes = {
        ...selectedAttributes,
        [attributeTitle]: valueText
      }

      // Check if this selection creates a valid variant combination
      const hasValidVariant = product?.product_variant?.some(variant => {
        return Object.entries(newAttributes).every(([attrTitle, attrValue]) => {
          return variant.attribute_value.some(attr =>
            attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
            attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
          )
        })
      })

      // If this selection doesn't create a valid variant combination, don't update
      if (!hasValidVariant) {
        console.log(`No valid variant found for selection: ${attributeTitle}=${valueText}`)
        return
      }

      // Update the selected attributes
      setSelectedAttributes(newAttributes)

      // Update extra data in cart store
      const newExtraData = {
        ...cartStore.getState().cartItem.extra_data,
        [attributeTitle]: valueText
      }
      setExtraData(newExtraData)

      // Find matching variants with the selected attributes and select the one with the lowest order
      const matchingVariants = product?.product_variant?.filter(variant => {
        // Check if this variant matches all selected attributes
        return Object.entries(newAttributes).every(([attrTitle, attrValue]) => {
          return variant.attribute_value.some(attr =>
            attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
            attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
          )
        })
      }) || []

      if (matchingVariants.length > 0) {
        // Sort by order and select the first one
        const sortedVariants = matchingVariants.sort((a, b) => a.order - b.order)
        handleVariantClick(sortedVariants[0])
      }
    }
  }

  // Check if an attribute value is available based on all currently selected attributes
  const isAttributeValueAvailable = (attributeTitle: string, valueText: string) => {
    if (!selectedVariant) return false

    // If no attributes are selected yet, all values are available
    if (Object.keys(selectedAttributes).length === 0) return true

    // If we're checking the availability of an attribute that's already selected,
    // it's obviously available
    if (selectedAttributes[attributeTitle]?.toLowerCase() === valueText.toLowerCase()) return true

    // Get all available values for this attribute based on current selections
    const availableValues = getAvailableAttributeValues(attributeTitle)

    // Check if the value we're checking is in the list of available values
    return availableValues.includes(valueText.toLowerCase())
  }

  // Handle Buy Now button click
  const handleBuyNow = (stockQty: number) => {
    if (stockQty <= 0) {
      alert("This item is temporarily out of stock.\n" +
        "We're expecting it to be back in stock soon.\n" +
        "In the meantime, you can add this to your cart to be notified when it's available again."
      )
      return
    }

    handleAddToCart()
    navigate('/checkout/cart')
  }

  return (
    <section className={styles.product_details}>
      <section className={styles.title_and_rating}>
        <h1 className={styles.product_title}>{product?.title}</h1>
        <div className={styles.rating_container}>
          <Rating product={product} color="#FF9900" />
          {/* <span className={styles.rating_count}>{product?.reviews.length} ratings</span> */}
        </div>
      </section>

      <hr className={styles.divider} />

      <section className={styles.price_container}>
        <div className={styles.discount_badge}>
          {calculateDiscount(selectedVariant?.price)}% off
        </div>
        <div className={styles.price}>
          <span className={styles.current_price}>${selectedVariant?.price?.toFixed(2)}</span>
          <span className={styles.original_price}>${priceWithTrickyDiscount(selectedVariant?.price)}</span>
        </div>
        <div className={styles.savings}>
          You save: ${(parseFloat(priceWithTrickyDiscount(selectedVariant?.price) || "0") - (selectedVariant?.price || 0)).toFixed(2)}
        </div>
      </section>

      <hr className={styles.divider} />

      {/* Primary Attribute Selector (e.g., Size) */}
      <section className={styles.product_variants}>
        <h3>
          {primaryAttributeTitle}:
          {selectedAttributes[primaryAttributeTitle] && (
            <span className={styles.selected_value_display}>
              {selectedAttributes[primaryAttributeTitle]}
            </span>
          )}
        </h3>
        <div className={styles.variants}>
          {/* Display unique primary attribute values (e.g., sizes) */}
          {groupedVariants.map((group) => {
            const isSelected = selectedAttributes[primaryAttributeTitle] === group.primaryValue
            return (
              <div
                key={group.primaryValue}
                className={`${isSelected ? `${styles.variant__highlight} ${styles.variant}` : `${styles.variant}`}`}
                onClick={() => handlePrimarySelection(group.primaryValue)}
                title={isSelected ? `Click to deselect ${primaryAttributeTitle} ${group.primaryValue}` : `Click to select ${primaryAttributeTitle} ${group.primaryValue}`}
                style={{ cursor: 'pointer' }}
              >
                <p>{group.primaryValue}</p>
                <p>${group.price.toFixed(2)}</p>
              </div>
            )
          })}
        </div>
      </section>

      {/* Secondary Attribute Selectors (e.g., Color) */}
      {secondaryAttributes.map((selector) => (
        <section
          key={selector.attribute_id}
          className={
            `${styles.option_selector} ` +
            (missingAttributes.length > 0 && missingAttributes[0] === selector.attribute_title ? styles.next_to_select : '')
          }
        >
          <h3 className={styles.selector_title}>
            {selector.attribute_title}:
            {selectedAttributes[selector.attribute_title] && (
              <span className={styles.selected_value_display}>
                {selectedAttributes[selector.attribute_title]}
              </span>
            )}
            {/* Show message if this is the next attribute to select and not selected yet */}
            {missingAttributes.length > 0 && missingAttributes[0] === selector.attribute_title && !selectedAttributes[selector.attribute_title] && (
              <span className={styles.missing_attr_msg}>
                Select a {selector.attribute_title}
              </span>
            )}
          </h3>
          <div className={styles.selector_values}>
            {selector.values.map(value => {
              const isAvailable = isAttributeValueAvailable(selector.attribute_title, value.value_text)
              const isSelected = selectedAttributes[selector.attribute_title] === value.value_text

              // Determine the appropriate title based on state
              let buttonTitle = value.value_text
              if (!isAvailable) {
                buttonTitle = `Not available with current selections`
              } else if (isSelected) {
                buttonTitle = `Click to deselect ${selector.attribute_title} ${value.value_text}`
              } else {
                buttonTitle = `Click to select ${selector.attribute_title} ${value.value_text}`
              }

              return (
                <button
                  key={value.value_id}
                  className={`
                    ${styles.selector_value}
                    ${isSelected ? styles.selected_value : ''}
                    ${!isAvailable ? styles.unavailable_value : ''}
                  `}
                  onClick={() => handleAttributeSelection(selector.attribute_title, value.value_id, value.value_text)}
                  disabled={!isAvailable}
                  title={buttonTitle}
                >
                  {selector.attribute_title.toLowerCase() === 'color' ? (
                    (() => {
                      // Find a variant with this color to get its image
                      const colorVariant = product?.product_variant?.find(variant => {
                        const variantColor = getVariantColorValue(variant)
                        return variantColor?.toLowerCase() === value.value_text.toLowerCase()
                      })

                      const colorImage = colorVariant ? getColorSelectionImage(colorVariant, import.meta.env.VITE_CLOUDINARY_URL) : null

                      return colorImage ? (
                        <div
                          className={`${styles.color_image_button} ${isSelected ? styles.selected_color_image : ''} ${!isAvailable ? styles.unavailable_color_image : ''}`}
                          title={buttonTitle}
                        >
                          <img
                            src={colorImage}
                            alt={`${value.value_text} color option`}
                            loading="lazy"
                          />
                        </div>
                      ) : (
                        // Fallback to color swatch if no image available
                        <div
                          className={styles.color_swatch}
                          style={{
                            backgroundColor: getFallbackColor(value.value_text),
                            opacity: isAvailable ? 1 : 0.3,
                            boxShadow: isSelected
                              ? `0 0 0 2px white, 0 0 0 4px ${getFallbackColor(value.value_text)}`
                              : 'none',
                            transform: isSelected ? 'scale(1.1)' : 'scale(1)',
                            transition: 'all 0.2s ease'
                          }}
                          title={buttonTitle}
                        />
                      )
                    })()
                  ) : (
                    value.value_text
                  )}
                </button>
              )
            })}
          </div>
        </section>
      ))}

      {/* If there are no option selectors, show the selectable attribute values */}
      {(!product?.option_selectors || product.option_selectors.length === 0) && (
        <section className={styles.selectable_attribute_values}>
          <SelectableAttributeValues selectableAttValues={product?.selectable_attribute_values} />
        </section>
      )}

      <section className={styles.stock_status}>
        <p className={`${styles.stock_indicator} ${selectedVariant?.stock_qty && selectedVariant.stock_qty < 15
          ? styles.low_stock
          : selectedVariant?.stock_qty === 0
            ? styles.out_of_stock
            : styles.in_stock
          }`}>
          {renderStockMessage(selectedVariant?.stock_qty || 0)}
        </p>
      </section>

      <hr className={styles.divider} />

      <section className={styles.product_quantity}>
        <p>Quantity:</p>
        <div className={styles.quantity__controls}>
          <button
            onClick={() => handleQtyChange(qty - 1)}
            disabled={qty <= 1}
          >
            <i><FiMinus /></i>
          </button>
          <input
            type="text"
            value={qty}
            onChange={(e) => {
              const value = parseInt(e.target.value)
              if (!isNaN(value) && value > 0) {
                handleQtyChange(value)
              }
            }}
            min={1}
            max={selectedVariant?.stock_qty || 10}
          />
          <button
            onClick={() => handleQtyChange(qty + 1)}
            disabled={qty >= (selectedVariant?.stock_qty || 10)}
          >
            <i><FiPlus /></i>
          </button>
        </div>
      </section>

      <section className={styles.checkout}>
        <button
          className={styles.add_to_cart_btn}
          type="submit"
          disabled={isAddToCartDisabled}
          onClick={handleAddToCart}
        >
          {isPending ? (
            <img src={loading_svg} alt="Loading..." className='loading_svg' />
          ) : (
            'Add to Cart'
          )}
        </button>

        <button
          className={styles.buy_now_btn}
          onClick={() => handleBuyNow(selectedVariant?.stock_qty || 0)}
          disabled={isAddToCartDisabled}
        >
          Buy Now
        </button>

        <div className={styles.wishlist_wrapper}>
          <WishlistButton productId={product.id} />
        </div>
      </section>

      {error && (
        <section>
          <Alert variant="error" message={error?.message} />
        </section>
      )}
    </section>
  )
}

export default ProductDetailsInfo

