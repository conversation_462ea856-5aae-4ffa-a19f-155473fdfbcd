import { Fa<PERSON><PERSON><PERSON>, Fa<PERSON>eg<PERSON>eart } from "react-icons/fa"
import { useNavigate } from 'react-router-dom'
import authStore from '../../../zustand/authStore'
import useToggleWishlist from '../../../react-query/hooks/wishlist-hooks/useToggleWishlist'
import useWishlist from '../../../react-query/hooks/wishlist-hooks/useWishlist'
import Tooltip from '../../utils/tooltip/Tooltip'
import styles from './WishlistButton.module.scss'


interface Props {
  productId: number
}

const WishlistButton = ({ productId }: Props) => {
  const mutation = useToggleWishlist()
  const navigate = useNavigate()
  const { isLoggedIn } = authStore()
  const { data: wishlistItems } = useWishlist(1, isLoggedIn)

  const isInWishlist = wishlistItems?.results.some(item => item.product.id === productId)

  const handleToggleWishlist = () => {
    if (!isLoggedIn) {
      navigate('/user/login/')
      return
    }
    mutation.mutate(productId)
  }

  return (
    <Tooltip
      content={isInWishlist ? "Remove from wishlist" : "Add to wishlist"}
      position="top"
    >
      <button
        onClick={handleToggleWishlist}
        className={styles.wishlist__button}
        disabled={mutation.isPending}
        aria-label={isInWishlist ? "Remove from wishlist" : "Add to wishlist"}
      >
        {isInWishlist ? <i><FaHeart /></i> : <i><FaRegHeart /></i>}
      </button>
    </Tooltip>
  )
}

export default WishlistButton
