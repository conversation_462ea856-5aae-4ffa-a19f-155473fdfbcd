import { FiTrash2 } from "react-icons/fi"
import { Link, useSearchParams } from "react-router-dom"
import <PERSON><PERSON> from '../../components/utils/alert/Alert'
import Spinner from '../../components/utils/spinner/Spinner'
import { ITEMS_PER_PAGE } from '../../react-query/hooks/constants'
import useDeleteWishlistItem from '../../react-query/hooks/wishlist-hooks/useDeleteWishlistItem'
import useWishlist from "../../react-query/hooks/wishlist-hooks/useWishlist"
import Pagination from '../utils/pagination/Pagination'
import styles from "./Wishlist.module.scss"
import authStore from "../../zustand/authStore"


const Wishlist = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const page = parseInt(searchParams.get("page") || "1", 10)
  const { isLoggedIn } = authStore()
  const { error, isPending, data } = useWishlist(page, isLoggedIn)
  const mutation = useDeleteWishlistItem()

  const handleDelete = (itemId: number) => {
    mutation.mutate(itemId)
  }

  const handlePageChange = (newPage: number) => {
    setSearchParams({ page: newPage.toString() })
  }

  return (
    <>
      {isPending ? (
        <Spinner color="#0091CF" size={20} loading={true} bgOpacity={0} />
      ) : error ? (
        <Alert variant="error" message={error.message} />
      ) : data?.results.length === 0 ? (
        <div className={`container ${styles.no_items}`}>
          <p>Your wishlist is currently empty.</p>
          <Link to="/">Continue Shopping</Link>
        </div>
      ) : (
        <div className={styles.wishlist}>
          <h2 className={styles.heading}>My Wishlist</h2>
          <div className={styles.wishlist__items}>
            {data?.results.map((item) => (
              <div
                key={item.id}
                className={`${styles.wishlist__item} ${!item.product.is_active ? styles.inactive : ''}`}
              >
                <div className={styles.product__image__container}>
                  <img
                    src={`https://res.cloudinary.com/dev-kani/${item.product.product_variant[0].product_image[0].image}`}
                    alt={item.product.product_variant[0].product_image[0].alternative_text}
                    className={styles.product__image}
                  />
                  {!item.product.is_active && (
                    <div className={styles.inactive__overlay}>
                      <span className={styles.inactive__text}>This item is not available</span>
                    </div>
                  )}
                </div>
                <div className={styles.item__details}>
                  <h3 className={styles.product__title}>{item.product.title}</h3>
                  <p className={styles.product__price}>${item.product.product_variant[0].price.toFixed(2)}</p>
                  <p className={styles.added__date}>Added on: {new Date(item.added_at).toLocaleDateString()}</p>
                  <Link
                    to={`/products/${item.product.slug}`}
                    className={`${styles.product__link} ${!item.product.is_active ? styles.disabled : ''}`}
                  >
                    View Product
                  </Link>
                  <button
                    className={styles.delete__button}
                    onClick={() => handleDelete(item.id)}
                    aria-label="Remove from wishlist"
                  >
                    <i><FiTrash2 /></i>
                  </button>
                </div>
              </div>
            ))}
          </div>
          {data && data.count > 0 && (
            <Pagination
              currentPage={page}
              totalPages={Math.ceil(data.count / ITEMS_PER_PAGE)}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      )}
    </>
  )
}

export default Wishlist
