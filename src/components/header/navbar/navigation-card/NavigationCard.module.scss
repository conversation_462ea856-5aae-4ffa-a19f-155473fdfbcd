@import '../../../../scss/mixins';
@import '../../../../scss/variables';

.nav__card {
  width: 100%;
  position: absolute;
  background-color: $sky-lighter-blue;
  padding: $padding-3;
  z-index: 3;
  border-radius: $border-radius-2 0;
  box-shadow: $box-shadow-blue-1;
  transition: all 0.3s ease;

  .list {
    list-style: none; // Default to none unless overridden by levels
    padding: 0;
    margin: 0;

    li {
      margin: $padding-1 0;

      a {
        display: block;
        padding: $padding-1 $padding-2;
        border-radius: $border-radius-1;
        font-weight: normal;
        color: $primary-dark-text-color;

        // transition: background-color 0.3s ease, color 0.3s ease;

        &:hover {
          // background-color: $sky-light-blue;
          // color: $primary-blue;
          text-decoration: underline;
        }
      }
    }
  }

  // Alternatively, use this reusable mixin for Level List Styling
  // @include levelStyles(5, $primary-dark, 15px);

  // Level 0 styles (Horizontal layout with wrapping)
  .level-0 {
    @include flexbox(flex-start, flex-start, row, wrap);
    gap: $padding-2;

    li {
      margin: 0;

      a {
        font-size: $font-size-4;
        font-weight: bold;
        color: $primary-dark-blue;
      }
    }
  }

  // Level 1 styles (Vertical layout)
  .level-1 {
    // list-style: disc; 
    margin-left: $padding-3;

    li {
      a {
        font-size: $font-size-3;
        font-weight: bold;
        color: $primary-blue;
      }
    }
  }

  // Level 2 styles (Vertical layout)
  .level-2 {
    // list-style: square; 
    // margin-left: $padding-4;

    li {
      a {
        font-size: $font-size-2;
        color: $primary-dark;
      }
    }
  }

  // Level 3 styles
  .level-3 {
    // list-style: circle; 
    margin-left: $padding-2;

    li {
      a {
        font-size: $font-size-2;
        font-style: italic;
        color: $primary-lighter-text-color;
      }
    }
  }

  @media (min-width: $mobile) {
    .level-0 {
      li {
        flex: 1 0 20%; // Items take up equal space and wrap as needed
      }
    }
  }
}