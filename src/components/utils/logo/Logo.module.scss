@use '../../../styles/variables.scss' as *;
@use '../../../styles/mixins.scss' as *;

.logo {
  @include flexbox(flex-start, center);
  font-weight: 700;
  column-gap: 1px;

  h1 {
    font-size: 28px;
    font-family: 'Poppins', sans-serif;
    font-weight: 800;
    color: $primary-blue;
  }

  span {
    font-size: 23px;
    background-color: $primary-blue;
    padding: 0 3px 0 3px;
    border-radius: 5px;
    color: #fff;
  }
}
