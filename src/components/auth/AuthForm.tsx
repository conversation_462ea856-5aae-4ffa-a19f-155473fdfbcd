// Shared form component for authentication
// Provides consistent form styling and error handling

import { ReactNode } from 'react';
import styles from './AuthForm.module.scss';

interface AuthFormProps {
  children: ReactNode;
  onSubmit: (e: React.FormEvent) => void;
  errorMessage?: string;
}

export function AuthForm({ children, onSubmit, errorMessage }: AuthFormProps) {
  return (
    <form onSubmit={onSubmit} className={styles.form}>
      {errorMessage && (
        <div className={styles.errorMessage}>
          {errorMessage}
        </div>
      )}
      {children}
    </form>
  );
}