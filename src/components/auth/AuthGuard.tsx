'use client';

// Authentication guard component for protecting routes
// Handles authentication and authorization checks
// Aligned with React-TS implementation

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '../../stores/auth-store';

interface AuthGuardProps {
  children: React.ReactNode;
  permission?: string;
  group?: string;
  requireAuth?: boolean;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  permission,
  group,
  requireAuth = true,
  fallback,
  redirectTo = '/auth/login',
}) => {
  const router = useRouter();
  const {
    isAuthenticated,
    isLoading,
    user,
  } = useAuthStore();

  // Show loading while checking authentication (prevents premature redirects)
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Only redirect to login if authentication is required, user is not authenticated, and we're not loading
  if (requireAuth && !isAuthenticated && !isLoading) {
    router.push(`${redirectTo}?redirect=${encodeURIComponent(window.location.pathname)}`);
    return null;
  }

  // If authenticated, check permissions
  if (isAuthenticated && user) {
    // Superuser bypasses all permission checks
    if (user.is_superuser) {
      return <>{children}</>;
    }

    // For now, we don't have permission/group checking implemented
    // This would be implemented when the backend supports it
    if (permission) {
      // TODO: Implement permission checking
      console.warn('Permission checking not yet implemented:', permission);
    }

    if (group) {
      // TODO: Implement group checking
      console.warn('Group checking not yet implemented:', group);
    }
  }

  return <>{children}</>;
};

interface PermissionGuardProps {
  children: React.ReactNode;
  permission: string;
  fallback?: React.ReactNode;
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  fallback,
}) => {
  const { user } = useAuthStore();

  // Superuser bypasses all checks
  if (user?.is_superuser) {
    return <>{children}</>;
  }

  // TODO: Implement permission checking
  console.warn('Permission checking not yet implemented:', permission);

  return <>{children}</>;
};

interface GroupGuardProps {
  children: React.ReactNode;
  group: string;
  fallback?: React.ReactNode;
}

export const GroupGuard: React.FC<GroupGuardProps> = ({
  children,
  group,
  fallback,
}) => {
  const { user } = useAuthStore();

  // Superuser bypasses all checks
  if (user?.is_superuser) {
    return <>{children}</>;
  }

  // TODO: Implement group checking
  console.warn('Group checking not yet implemented:', group);

  return <>{children}</>;
};

interface RoleGuardProps {
  children: React.ReactNode;
  roles: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  roles,
  requireAll = false,
  fallback,
}) => {
  const { user } = useAuthStore();

  // Superuser bypasses all checks
  if (user?.is_superuser) {
    return <>{children}</>;
  }

  // TODO: Implement role checking
  console.warn('Role checking not yet implemented:', roles);

  return <>{children}</>;
};
