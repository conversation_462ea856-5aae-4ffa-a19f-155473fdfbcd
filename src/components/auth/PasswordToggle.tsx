// Password visibility toggle component
// Reusable component for showing/hiding password fields

import { FiEye, FiEyeOff } from 'react-icons/fi';
import styles from './PasswordToggle.module.scss';

interface PasswordToggleProps {
  isVisible: boolean;
  onToggle: () => void;
}

export function PasswordToggle({ isVisible, onToggle }: PasswordToggleProps) {
  return (
    <button
      type="button"
      onClick={onToggle}
      className={styles.passwordToggle}
      aria-label={isVisible ? 'Hide password' : 'Show password'}
    >
      {isVisible ? <FiEyeOff size={18} /> : <FiEye size={18} />}
    </button>
  );
}