@use '../../styles/variables.scss' as *;
@use '../../styles/mixins.scss' as *;

.authLayout {
  min-height: 100vh;
  @include flexbox(center, center);
  background: linear-gradient(135deg, $gray-50 0%, $gray-100 100%);
  padding: $spacing-4;
}

.container {
  width: 100%;
  max-width: 480px;
}

.card {
  @include card;
  padding: $spacing-8;
  background: white;
  
  @include mobile-only {
    padding: $spacing-6;
  }
}

.header {
  text-align: center;
  margin-bottom: $spacing-8;
}

.title {
  font-size: $font-size-5;
  font-weight: 700;
  color: $primary-dark-text-color;
  margin-bottom: $spacing-2;
  margin-top: 0;
}

.subtitle {
  color: $primary-lighter-text-color;
  margin: 0;
  font-size: $font-size-2;
}

.content {
  margin-bottom: $spacing-6;
}

.footer {
  text-align: center;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}