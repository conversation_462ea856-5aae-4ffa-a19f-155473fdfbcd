// Shared layout component for authentication pages
// Provides consistent styling and structure for login/register forms

import { ReactNode } from 'react';
import styles from './AuthLayout.module.scss';

interface AuthLayoutProps {
  children: ReactNode;
  title: string;
  subtitle: string;
  footerContent?: ReactNode;
}

export function AuthLayout({ children, title, subtitle, footerContent }: AuthLayoutProps) {
  return (
    <div className={styles.authLayout}>
      <div className={styles.container}>
        <div className={styles.card}>
          <div className={styles.header}>
            <h1 className={styles.title}>{title}</h1>
            <p className={styles.subtitle}>{subtitle}</p>
          </div>
          
          <div className={styles.content}>
            {children}
          </div>
          
          {footerContent && (
            <div className={styles.footer}>
              {footerContent}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}