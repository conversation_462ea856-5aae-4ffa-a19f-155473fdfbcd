// Order management hooks using TanStack Query
// Provides reactive order data and mutations with caching

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { orderService } from '../services/order-service';
import { queryKeys } from '../services/query-keys';
import { useNotifications } from '../stores/ui-store';
import { OrderFilters, OrderStatusUpdate } from '../types/api-types';

/**
 * Hook to fetch paginated orders list
 */
export const useOrders = (filters: OrderFilters = {}) => {
  return useQuery({
    queryKey: queryKeys.orders.list(filters),
    queryFn: () => orderService.getOrders(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
  });
};

/**
 * Hook to fetch single order by ID
 */
export const useOrder = (orderId: number) => {
  return useQuery({
    queryKey: queryKeys.orders.detail(orderId),
    queryFn: () => orderService.getOrder(orderId),
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to fetch orders assigned to current user
 */
export const useMyAssignedOrders = (filters: Omit<OrderFilters, 'assigned_to'> = {}) => {
  return useQuery({
    queryKey: queryKeys.orders.myAssignments(),
    queryFn: () => orderService.getMyAssignedOrders(filters),
    staleTime: 1 * 60 * 1000, // 1 minute - more frequent updates for assigned orders
  });
};

/**
 * Hook to update order status
 */
export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ orderId, data }: { orderId: number; data: OrderStatusUpdate }) =>
      orderService.updateOrderStatus(orderId, data),
    onSuccess: (updatedOrder, { orderId }) => {
      showSuccess('Status Updated', 'Order status has been successfully updated.');
      
      // Update the order in cache
      queryClient.setQueryData(queryKeys.orders.detail(orderId), updatedOrder);
      
      // Invalidate orders lists to refresh counts and filters
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.lists() });
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.myAssignments() });
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update order status');
    },
  });
};

/**
 * Hook to assign order to staff member
 */
export const useAssignOrder = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ orderId, staffId }: { orderId: number; staffId: number }) =>
      orderService.assignOrder(orderId, staffId),
    onSuccess: (updatedOrder, { orderId }) => {
      showSuccess('Order Assigned', 'Order has been successfully assigned.');
      
      // Update the order in cache
      queryClient.setQueryData(queryKeys.orders.detail(orderId), updatedOrder);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.lists() });
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.myAssignments() });
    },
    onError: (error: any) => {
      showError('Assignment Failed', error.message || 'Failed to assign order');
    },
  });
};

/**
 * Hook for bulk order status updates
 */
export const useBulkUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ orderIds, status, notes }: { orderIds: number[]; status: string; notes?: string }) =>
      orderService.bulkUpdateStatus(orderIds, status, notes),
    onSuccess: (result) => {
      showSuccess(
        'Bulk Update Complete',
        `Successfully updated ${result.updated} orders. ${result.failed > 0 ? `${result.failed} failed.` : ''}`
      );
      
      // Invalidate all order-related queries to refresh data
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all });
    },
    onError: (error: any) => {
      showError('Bulk Update Failed', error.message || 'Failed to update orders');
    },
  });
};

/**
 * Hook to add order note
 */
export const useAddOrderNote = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ orderId, note, isInternal }: { orderId: number; note: string; isInternal?: boolean }) =>
      orderService.addOrderNote(orderId, note, isInternal),
    onSuccess: (_, { orderId }) => {
      showSuccess('Note Added', 'Order note has been added successfully.');
      
      // Invalidate order details to refresh notes
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.detail(orderId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.notes(orderId) });
    },
    onError: (error: any) => {
      showError('Failed to Add Note', error.message || 'Failed to add order note');
    },
  });
};

/**
 * Hook to cancel order
 */
export const useCancelOrder = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ orderId, reason }: { orderId: number; reason: string }) =>
      orderService.cancelOrder(orderId, reason),
    onSuccess: (updatedOrder, { orderId }) => {
      showSuccess('Order Cancelled', 'Order has been successfully cancelled.');
      
      // Update the order in cache
      queryClient.setQueryData(queryKeys.orders.detail(orderId), updatedOrder);
      
      // Invalidate orders lists
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.lists() });
    },
    onError: (error: any) => {
      showError('Cancellation Failed', error.message || 'Failed to cancel order');
    },
  });
};

/**
 * Hook to generate order invoice
 */
export const useGenerateInvoice = () => {
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (orderId: number) => orderService.generateInvoice(orderId),
    onSuccess: (blob, orderId) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice-order-${orderId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      showSuccess('Invoice Generated', 'Invoice has been generated and downloaded.');
    },
    onError: (error: any) => {
      showError('Invoice Generation Failed', error.message || 'Failed to generate invoice');
    },
  });
};

/**
 * Hook to generate shipping label
 */
export const useGenerateShippingLabel = () => {
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (orderId: number) => orderService.generateShippingLabel(orderId),
    onSuccess: (blob, orderId) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `shipping-label-order-${orderId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      showSuccess('Shipping Label Generated', 'Shipping label has been generated and downloaded.');
    },
    onError: (error: any) => {
      showError('Label Generation Failed', error.message || 'Failed to generate shipping label');
    },
  });
};

/**
 * Hook to export orders
 */
export const useExportOrders = () => {
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (filters: OrderFilters) => orderService.exportOrders(filters),
    onSuccess: (blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `orders-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      showSuccess('Export Complete', 'Orders have been exported and downloaded.');
    },
    onError: (error: any) => {
      showError('Export Failed', error.message || 'Failed to export orders');
    },
  });
};
