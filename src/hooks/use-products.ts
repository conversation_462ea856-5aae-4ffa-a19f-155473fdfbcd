// Products hooks using TanStack Query
// Provides reactive product data management

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../services/product-service'
import { useNotifications } from '../stores/ui-store'
import { queryKeys } from '../services/query-keys'
import type {
  Product,
  ProductFilters,
  ProductTypeAttributeAssociation,
  BrandProductType
} from '../types/api-types'

/**
 * Hook for fetching paginated products list
 */
export const useProducts = (filters?: ProductFilters) => {
  return useQuery({
    queryKey: queryKeys.products.list(filters),
    queryFn: () => ProductService.getProducts(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    // placeholderData: (previousData) => previousData,
  })
}

/**
 * Hook for fetching single product
 */
export const useProduct = (id: number) => {
  return useQuery({
    queryKey: queryKeys.products.detail(id),
    queryFn: () => ProductService.getProduct(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for fetching full product details including variants, images, and attribute values
 */
export const useFullProductDetails = (id: number) => {
  return useQuery({
    queryKey: queryKeys.products.fullDetail(id),
    queryFn: () => ProductService.getFullProductDetails(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for creating new product
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createProduct,
    onSuccess: (newProduct) => {
      showSuccess('Product Created', `${newProduct.title} has been created successfully.`)

      // Invalidate products list
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })

      // Add to cache
      queryClient.setQueryData(
        queryKeys.products.detail(newProduct.id),
        newProduct
      )
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create product.')
    },
  })
}

/**
 * Hook for updating product
 */
export const useUpdateProduct = (id: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: Partial<Product>) => ProductService.updateProduct(id, data),
    onSuccess: (updatedProduct) => {
      showSuccess('Product Updated', `${updatedProduct.title} has been updated successfully.`)

      // Update cached data
      queryClient.setQueryData(
        queryKeys.products.detail(id),
        updatedProduct
      )

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product.')
    },
  })
}

/**
 * Hook for updating product details only
 */
export const useUpdateProductDetails = (id: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: Partial<Product>) => ProductService.updateProduct(id, data),
    onSuccess: (updatedProduct) => {
      showSuccess('Product Details Updated', 'Product details have been updated successfully.')

      // Update cached data
      queryClient.setQueryData(
        queryKeys.products.detail(id),
        updatedProduct
      )
      queryClient.setQueryData(
        queryKeys.products.fullDetail(id),
        updatedProduct
      )

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product details.')
    },
  })
}

/**
 * Hook for deleting product
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteProduct,
    onSuccess: (_, productId) => {
      showSuccess('Product Deleted', 'Product has been deleted successfully.')

      // Remove from cache
      queryClient.removeQueries({ queryKey: queryKeys.products.detail(productId) })

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete product.')
    },
  })
}

/**
 * Hook for bulk product operations
 */
export const useBulkProductOperation = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.bulkOperation,
    onSuccess: (result, { action, productIds }) => {
      const count = productIds.length
      showSuccess(
        'Bulk Operation Complete',
        `Successfully ${action} ${count} product${count > 1 ? 's' : ''}.`
      )

      // Invalidate all product queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Bulk Operation Failed', error.message || 'Failed to complete bulk operation.')
    },
  })
}




