import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { z } from "zod"
import { useAuthStore } from '../../stores/auth-store'
import { useUIActions } from '../../stores/ui-store'
import { AuthService } from '../../services/api-client'
import { getErrorMessage } from '../../components/utils/getErrorMessage'

// Email validation schema
const emailSchema = z.string().email()

// Phone number validation schema (supports international formats)
const phoneNumberSchema = z.string().regex(
  /^(\+\d{1,3}[- ]?)?\d{10}$/,
  'Invalid phone number format'
)

const loginSchema = z.object({
  username: z
    .string()
    .min(1, { message: "Enter a valid email or phone number." })
    .transform((value) => value.replace(/\s+/g, ''))
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number",
      }
    ),
  password: z.string().min(1, 'Password is required'),
})

export type LoginUserShape = z.infer<typeof loginSchema>

const useLogin = () => {
  const { setUser, setAuthenticated } = useAuthStore()
  const { addNotification } = useUIActions()
  const queryClient = useQueryClient()
  const router = useRouter()

  const mutation = useMutation({
    mutationFn: (data: LoginUserShape) => AuthService.login(data),
    onSuccess: (response) => {
      setUser(response.user)
      setAuthenticated(true)
      
      addNotification({
        type: 'success',
        title: 'Welcome back!',
        message: 'You have been successfully logged in.',
      })

      // Invalidate auth-related queries
      queryClient.invalidateQueries({ queryKey: ['auth'] })
    },
    onError: (error: any) => {
      const errorMessage = getErrorMessage(error)
      addNotification({
        type: 'error',
        title: 'Login Failed',
        message: errorMessage,
      })
    }
  })

  return { mutation }
}

export default useLogin
