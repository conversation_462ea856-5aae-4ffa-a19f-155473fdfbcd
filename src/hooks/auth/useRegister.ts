import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { z } from "zod"
import { useAuthStore } from '../../stores/auth-store'
import { useUIActions } from '../../stores/ui-store'
import { AuthService } from '../../services/api-client'
import { getErrorMessage } from '../../components/utils/getErrorMessage'

// Email validation schema
const emailSchema = z.string().email()

// Phone number validation schema (supports international formats)
const phoneNumberSchema = z.string().regex(
  /^(\+\d{1,3}[- ]?)?\d{10}$/,
  'Invalid phone number format'
)

const registerSchema = z.object({
  username: z
    .string()
    .min(1, { message: "Email or phone number is required" })
    .transform((value) => value.replace(/\s+/g, ''))
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number",
      }
    ),
})

export type RegisterUserShape = z.infer<typeof registerSchema>

const useRegister = () => {
  const { setUser, setAuthenticated } = useAuthStore()
  const { addNotification } = useUIActions()
  const queryClient = useQueryClient()
  const router = useRouter()

  const mutation = useMutation({
    mutationFn: (data: RegisterUserShape) => {
      // For now, simulate the initiate registration API call
      // In a full implementation, this would call the actual registration API
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ username: data.username, message: 'Registration initiated' })
        }, 1000)
      })
    },
    onSuccess: (response: any) => {
      addNotification({
        type: 'success',
        title: 'Registration Initiated!',
        message: 'Please check your email/phone for verification code.',
      })

      // In a full implementation, you might store the username for the next step
      // setUsername(response.username)
    },
    onError: (error: any) => {
      const errorMessage = getErrorMessage(error)
      addNotification({
        type: 'error',
        title: 'Registration Failed',
        message: errorMessage,
      })
    }
  })

  return { mutation }
}

export default useRegister
