import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../../services/product-service';
import { useNotifications } from '../../stores/ui-store';
import { queryKeys } from '../../services/query-keys';
import type { Category } from '../../types/api-types';

/**
 * Hook for fetching product categories
 */
export const useProductCategories = () => {
  return useQuery({
    queryKey: queryKeys.products.categories(),
    queryFn: ProductService.getCategories,
    staleTime: 15 * 60 * 1000, // 15 minutes - categories don't change often
  });
};

/**
 * Hook for creating a new category
 */
export const useCreateCategory = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.createCategory,
    onSuccess: (newCategory) => {
      showSuccess('Category Created', `${newCategory.name} has been created successfully.`);
      queryClient.invalidateQueries({ queryKey: queryKeys.products.categories() });
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create category.');
    },
  });
};

/**
 * Hook for updating a category
 */
export const useUpdateCategory = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Category> }) =>
      ProductService.updateCategory(id, data),
    onSuccess: (updatedCategory) => {
      showSuccess('Category Updated', `${updatedCategory.name} has been updated successfully.`);
      queryClient.invalidateQueries({ queryKey: queryKeys.products.categories() });
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update category.');
    },
  });
};

/**
 * Hook for deleting a category
 */
export const useDeleteCategory = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteCategory(id),
    onSuccess: (_, categoryId) => {
      showSuccess('Category Deleted', 'Category has been deleted successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.categories() });
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete category.');
    },
  });
};
