import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../../services/product-service';
import { useNotifications } from '../../stores/ui-store';
import { queryKeys } from '../../services/query-keys';
import type { Brand } from '../../types/api-types';

/**
 * Hook for fetching product brands
 */
export const useProductBrands = () => {
  return useQuery({
    queryKey: queryKeys.products.brands(),
    queryFn: ProductService.getBrands,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

/**
 * Hook for creating a new brand
 */
export const useCreateBrand = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.createBrand,
    onSuccess: (newBrand) => {
      showSuccess('Brand Created', `${newBrand.title} has been created successfully.`);
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() });
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create brand.');
    },
  });
};

/**
 * Hook for updating a brand
 */
export const useUpdateBrand = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Brand> }) =>
      ProductService.updateBrand(id, data),
    onSuccess: (updatedBrand) => {
      showSuccess('Brand Updated', `${updatedBrand.title} has been updated successfully.`);
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() });
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update brand.');
    },
  });
};

/**
 * Hook for deleting a brand
 */
export const useDeleteBrand = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteBrand(id),
    onSuccess: (_, brandId) => {
      showSuccess('Brand Deleted', 'Brand has been deleted successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() });
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete brand.');
    },
  });
};
