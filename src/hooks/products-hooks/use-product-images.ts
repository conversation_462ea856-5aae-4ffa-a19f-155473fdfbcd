import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../../services/product-service';
import { useNotifications } from '../../stores/ui-store';
import { queryKeys } from '../../services/query-keys';
import type { ProductImageCreateData } from '../../types/api-types';

/**
 * Hook for fetching product images
 */
export const useProductImages = (variantId: number) => {
  return useQuery({
    queryKey: queryKeys.products.images(variantId),
    queryFn: () => ProductService.getProductImages(variantId),
    enabled: !!variantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for uploading product image
 */
export const useUploadProductImage = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.uploadProductImage,
    onSuccess: (newImage) => {
      showSuccess('Image Uploaded', 'Product image has been uploaded successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.images(newImage.product_variant) });
    },
    onError: (error: any) => {
      showError('Upload Failed', error.message || 'Failed to upload product image.');
    },
  });
};

/**
 * Hook for updating product image
 */
export const useUpdateProductImage = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ProductImageCreateData> }) =>
      ProductService.updateProductImage(id, data),
    onSuccess: (updatedImage) => {
      showSuccess('Image Updated', 'Product image has been updated successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.images(updatedImage.product_variant) });
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product image.');
    },
  });
};

/**
 * Hook for deleting product image
 */
export const useDeleteProductImage = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteProductImage(id),
    onSuccess: () => {
      showSuccess('Image Deleted', 'Product image has been deleted successfully.');
      // We need to invalidate the appropriate queries, but we don't have the variant ID here
      // The parent component should handle this invalidation
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete product image.');
    },
  });
};

/**
 * Hook for individual image reorder
 */
export const useReorderProductImage = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ imageId, newOrder }: { imageId: number; newOrder: number }) =>
      ProductService.reorderProductImage(imageId, newOrder),
    onSuccess: () => {
      showSuccess('Reordered', 'Product image has been reordered successfully.');
      // Invalidate the appropriate queries - the parent component should handle this
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product image.');
    },
  });
};

/**
 * Hook for drag-and-drop reordering product images
 */
export const useReorderProductImagesDragDrop = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ productVariantId, orderedIds }: { productVariantId: number; orderedIds: number[] }) =>
      ProductService.reorderProductImagesDragDrop(productVariantId, orderedIds),
    onSuccess: (_, variables) => {
      showSuccess('Reordered', 'Product images have been reordered successfully.');
      // Invalidate the appropriate queries - the parent component should handle this
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.products.images(variables.productVariantId) 
      });
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product images.');
    },
  });
};