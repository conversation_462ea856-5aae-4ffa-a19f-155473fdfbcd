import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../../services/product-service'
import { useNotifications } from '../../stores/ui-store'
import { queryKeys } from '../../services/query-keys'
import type { ProductVariant, ProductVariantCreateData } from '../../types/api-types'

/**
 * Hook for fetching product variants for a specific product
 */
export const useProductVariants = (productId: number) => {
  return useQuery({
    queryKey: queryKeys.products.variants(productId),
    queryFn: () => ProductService.getProductVariants(productId),
    enabled: !!productId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for updating a product variant
 */
export const useUpdateProductVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ProductVariant> }) =>
      ProductService.updateVariant(id, data),
    onSuccess: (updatedVariant) => {
      showSuccess('Variant Updated', `Variant ${updatedVariant.sku} has been updated successfully.`)
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update variant.')
    },
  })
}

/**
 * Hook for creating a new product variant
 */
export const useCreateVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (variantData: Partial<ProductVariant>) =>
      ProductService.createVariant(variantData),
    onSuccess: (createdVariant) => {
      showSuccess('Variant Created', `Variant ${createdVariant.sku} has been created successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create variant.')
    },
  })
}

/**
 * Hook for deleting a product variant
 */
export const useDeleteVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteVariant(id),
    onSuccess: () => {
      showSuccess('Variant Deleted', 'Variant has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete variant.')
    },
  })
}

// Alias for backward compatibility
// export const useDeleteProductVariant = useDeleteVariant;

/**
 * Hook for creating a product with variants in a single operation
 */
export const useCreateProductWithVariants = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: any) => ProductService.createProductWithVariants(data),
    onSuccess: (response) => {
      showSuccess('Product Created', 'Product and variants have been created successfully.')
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
      if (response?.product?.id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.products.detail(response.product.id) })
      }
      return response
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create product with variants.')
      throw error
    },
  })
}


/**
 * Hook for reordering a single product variant
 */
export const useReorderProductVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ variantId, newOrder }: { variantId: number; newOrder: number }) =>
      ProductService.reorderProductVariant(variantId, newOrder),
    onSuccess: () => {
      showSuccess('Reordered', 'Product variant has been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product variant.')
    },
  })
}

/**
 * Hook for drag-and-drop reordering of product variants
 */
export const useReorderProductVariantsDragDrop = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productId, orderedIds }: { productId: number; orderedIds: number[] }) =>
      ProductService.reorderProductVariantsDragDrop(productId, orderedIds),
    onSuccess: () => {
      showSuccess('Reordered', 'Product variants have been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product variants.')
    },
  })
}