import { create<PERSON>rowser<PERSON>outer } from "react-router-dom"
import Layout from "./components/layouts/Layout"
import Wishlist from "./components/wishlist/Wishlist"
import Activation from "./pages/auth/activation/Activation"
import ChangeExistingPassword from "./pages/auth/change-existing-password/ChangeExistingPassword"
import Login from "./pages/auth/login/Login"
import ChangeEmail from "./pages/auth/profile/change-auth-info/ChangeEmail"
import ChangePhoneNumber from "./pages/auth/profile/change-auth-info/ChangePhoneNumber"
import VerifyAuthInfo from "./pages/auth/profile/change-auth-info/VerifyAuthInfo"
import Profile from "./pages/auth/profile/Profile"
import InitiateRegistration from "./pages/auth/register/1-initiate-registration/InitiateRegistration"
import VerifyRegCredentials from "./pages/auth/register/2-verify-reg-credentials/VerifyRegCredentials"
import SetPassword from "./pages/auth/register/3-set-password/SetPassword"
import UpdateCustomer from "./pages/auth/register/4-customer/UpdateCustomer"
import { UpdateAuthInfoContainer } from "./pages/auth/register/5-update-auth-info/UpdateAuthInfo"
import PasswordReset from "./pages/auth/resetting-forgotten-password/PasswordReset"
import Cart from "./pages/checkout-process/1-cart/Cart"
import AddressStage from "./pages/checkout-process/2-address-choices/AddressStage"
import PlaceOrder from "./pages/checkout-process/3-payment-choices/PlaceOrder"
import Order from "./pages/checkout-process/4-order/Order"
import Home from "./pages/home/<USER>"
import MyAccount from "./pages/my-account/MyAccount"
import OrderList from "./pages/orders-list/OrderList"
import ProductDetails from "./pages/product-related/product-details/ProductDetails"
import ProductList from "./pages/product-related/product-list/ProductList"
import ReviewDetails from "./pages/product-related/product-reviews/ReviewDetails"


export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    // element: <Testing123 />,
    // errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <Home />
      },
      {
        path: `category/:slug`,
        element: <ProductList />
      },
      {
        // Add this route for products listing
        path: 'products',
        element: <ProductList />
      },
      {
        path: `products/:slug`,
        element: <ProductDetails />
      },
      {
        path: `products/:slug/:id`,
        element: <ReviewDetails />
      },
      // {
      //   path: 'products/search',
      //   element: <ProductList />,
      // },
      {
        path: `cart`,
        element: <Cart />
      },
      {
        path: `my-orders`,
        element: <OrderList />
      },
      {
        path: 'customer',
        element: <MyAccount />,
        children: [
          {
            index: true,
            element: <Profile />
          },
          {
            path: `my-orders`,
            element: <OrderList />
          },
          {
            path: `wishlist`,
            element: <Wishlist />
          }
        ]
      }
    ]
  },
  {
    path: `checkout`,
    children: [
      {
        // path: 'address-choices',
        index: true,
        element: <AddressStage />
      },
      {
        path: 'place-order',
        element: <PlaceOrder />
      },
      {
        path: 'order/:id',
        element: <Order />
      },
    ]
  },
  {
    path: 'user',
    // element: 
    children: [
      {
        path: `register`,
        element: <InitiateRegistration />
      },
      {
        path: `login`,
        element: <Login />
      },
      // Account activation
      {
        path: `activate/:uid/:token`,
        element: <Activation />
      },
      // Change Email Address
      {
        path: `change-email`,
        element: <ChangeEmail />
      },
      {
        path: `change-phone-number`,
        element: <ChangePhoneNumber />
      },
      {
        path: `submit-auth-info-verify-code`,
        element: <VerifyAuthInfo />
      },
      // {
      //   path: `confirm-email/:token`,
      //   element: <ConfirmEmailChange />
      // },
      // Change to a new Password
      {
        path: `new-password`,
        element: <ChangeExistingPassword />
      },
      // Forgotten Password Reset
      {
        path: `password-reset`,
        element: <PasswordReset />
      },
      // {
      //   path: `:uid/:token`,
      //   element: <ResetToNewPassword />
      // },
      {
        path: `verify-reg-credentials`,
        element: <VerifyRegCredentials />
      },
      {
        path: `update-auth-info`,
        element: <UpdateAuthInfoContainer />
      },
      {
        path: `update-customer`,
        element: <UpdateCustomer />
      },
      {
        path: `set-password`,
        element: <SetPassword />
      },
    ]
  }
])