// Essential SCSS Variables

// Typography
$primary-font-family: '<PERSON> Sans', '<PERSON> Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;

// Font Sizes
$font-size-1: 12px;
$font-size-2: 16px;
$font-size-3: 18px;
$font-size-4: 20px;
$font-size-5: 24px;

// Primary Colors
$primary-blue: #0091cf;
$primary-blue-light: #00b3ff;
$primary-blue-dark: #006ba3;
$primary-dark: #131921;
$primary-yellow: #FFD814;
$primary-red: #cf0707;
$primary-green: #2E9F1C;

// Neutral Colors
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;

// Text Colors
$primary-dark-text-color: #333;
$primary-lighter-text-color: #666;

// Status Colors
$success: #10b981;
$warning: #f59e0b;
$error: #ef4444;
$info: #3b82f6;

// Alert Colors (matching React-TS implementation)
$info-bg: #bedeff;
$info-text: #084298;
$warning-bg: #ffe180;
$warning-text: #534000;
$error-bg: #ffb2b9;
$error-text: #580007;
$success-bg: #9fffa3;
$success-text: #002e02;

// Additional Colors from React-TS
$primary-dark-blue: #232F3E;

// Spacing
$spacing-1: 4px;
$spacing-2: 8px;
$spacing-3: 12px;
$spacing-4: 16px;
$spacing-5: 20px;
$spacing-6: 24px;
$spacing-8: 32px;
$spacing-12: 48px;
$spacing-16: 64px;
$spacing-20: 80px;

// Border Radius
$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-full: 9999px;

// Box Shadows
$box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);

// Transitions
$transition-fast: 0.15s ease-in-out;
$transition-normal: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;

// Media Queries
$mobile: 576px;
$tablet: 768px;
$laptop: 992px;
$desktop: 1200px;

// Z-index
$z-dropdown: 1000;
$z-modal: 1050;
$z-modal-backdrop: 1040;

// Transitions
$transition-fast: 150ms ease-in-out;
$transition-normal: 250ms ease-in-out;