// SSR-safe utilities for Zustand stores
// Provides safe access to stores during server-side rendering

import { useEffect, useState } from 'react'

/**
 * Hook for SSR-safe store access
 * Returns fallback value during SSR, actual store value on client
 */
export function useSSRSafeStore<T>(
  storeHook: () => T,
  fallback: T
): T {
  const [isClient, setIsClient] = useState(false)
  
  useEffect(() => {
    setIsClient(true)
  }, [])
  
  const storeValue = storeHook()
  
  return isClient ? storeValue : fallback
}

/**
 * Hook for SSR-safe boolean store values
 * Returns false during SSR, actual value on client
 */
export function useSSRSafeBooleanStore(
  storeHook: () => boolean
): boolean {
  return useSSRSafeStore(storeHook, false)
}

/**
 * Hook for SSR-safe array store values
 * Returns empty array during SSR, actual value on client
 */
export function useSSRSafeArrayStore<T>(
  storeHook: () => T[]
): T[] {
  return useSSRSafeStore(storeHook, [])
}

/**
 * Hook for SSR-safe string store values
 * Returns empty string during SSR, actual value on client
 */
export function useSSRSafeStringStore(
  storeHook: () => string
): string {
  return useSSRSafeStore(storeHook, '')
}

/**
 * Hook for SSR-safe theme store values
 * Returns 'light' during SSR, actual value on client
 */
export function useSSRSafeThemeStore(
  storeHook: () => 'light' | 'dark'
): 'light' | 'dark' {
  return useSSRSafeStore(storeHook, 'light')
}

/**
 * Hook for SSR-safe number store values
 * Returns 0 during SSR, actual value on client
 */
export function useSSRSafeNumberStore(
  storeHook: () => number
): number {
  return useSSRSafeStore(storeHook, 0)
}
