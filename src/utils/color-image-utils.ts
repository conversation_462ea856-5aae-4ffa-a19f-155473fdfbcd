import { ProductShape, ProductVariant } from '../types/product-types'

/**
 * Gets the primary image for a color variant to use in color selection
 * @param variant - The product variant
 * @param cloudinaryUrl - Base Cloudinary URL
 * @returns Image URL or null if no image available
 */
export const getColorSelectionImage = (variant: ProductVariant, cloudinaryUrl: string): string | null => {
  if (!variant.product_image || variant.product_image.length === 0) {
    return null
  }

  // Sort images by order and get the first one (primary image)
  const sortedImages = variant.product_image.sort((a, b) => a.order - b.order)
  const primaryImage = sortedImages[0]

  return `${cloudinaryUrl}/${primaryImage.image}`
}

/**
 * Gets color attribute value from a variant
 * @param variant - The product variant
 * @returns Color value or null if no color attribute found
 */
export const getVariantColorValue = (variant: ProductVariant): string | null => {
  const colorAttribute = variant.attribute_value.find(
    attr => attr.attribute.title.toLowerCase() === 'color'
  )
  return colorAttribute ? colorAttribute.attribute_value : null
}

/**
 * Groups variants by color and returns the representative variant for each color
 * @param variants - Array of product variants
 * @returns Map of color value to representative variant
 */
export const getColorVariantMap = (variants: ProductVariant[]): Map<string, ProductVariant> => {
  const colorMap = new Map<string, ProductVariant>()

  variants.forEach(variant => {
    const colorValue = getVariantColorValue(variant)
    if (colorValue && !colorMap.has(colorValue)) {
      colorMap.set(colorValue, variant)
    }
  })

  return colorMap
}

/**
 * Gets all unique color values from product variants
 * @param product - The product object
 * @returns Array of unique color values
 */
export const getUniqueColors = (product: ProductShape): string[] => {
  const colors = new Set<string>()

  product.product_variant?.forEach(variant => {
    const colorValue = getVariantColorValue(variant)
    if (colorValue) {
      colors.add(colorValue)
    }
  })

  return Array.from(colors)
}

/**
 * Finds variants that match a specific color
 * @param product - The product object
 * @param colorValue - The color value to match
 * @returns Array of variants with the specified color
 */
export const getVariantsByColor = (product: ProductShape, colorValue: string): ProductVariant[] => {
  return product.product_variant?.filter(variant => {
    const variantColor = getVariantColorValue(variant)
    return variantColor?.toLowerCase() === colorValue.toLowerCase()
  }) || []
}

/**
 * Gets the best representative variant for a color (prioritizes variants with images)
 * @param variants - Array of variants with the same color
 * @returns The best representative variant
 */
export const getBestColorRepresentative = (variants: ProductVariant[]): ProductVariant | null => {
  if (variants.length === 0) return null

  // Prioritize variants with images
  const variantsWithImages = variants.filter(v => v.product_image && v.product_image.length > 0)

  if (variantsWithImages.length > 0) {
    // Sort by order and return the first one
    return variantsWithImages.sort((a, b) => a.order - b.order)[0]
  }

  // Fallback to first variant sorted by order
  return variants.sort((a, b) => a.order - b.order)[0]
}

/**
 * Checks if a color value can be used as a valid CSS color
 * @param colorValue - The color value to check
 * @returns True if it's a valid CSS color
 */
export const isValidCSSColor = (colorValue: string): boolean => {
  // Create a temporary element to test the color
  const tempElement = document.createElement('div')
  tempElement.style.color = colorValue.toLowerCase()
  return tempElement.style.color !== ''
}

/**
 * Gets a fallback color for color swatches when image is not available
 * @param colorValue - The color value
 * @returns A valid CSS color or a default color
 */
export const getFallbackColor = (colorValue: string): string => {
  const lowerColor = colorValue.toLowerCase()

  // Common color mappings
  const colorMap: Record<string, string> = {
    'dark sea green': '#8FBC8F',
    'sea green': '#2E8B57',
    'light blue': '#ADD8E6',
    'dark blue': '#00008B',
    'light gray': '#D3D3D3',
    'dark gray': '#A9A9A9',
    'light grey': '#D3D3D3',
    'dark grey': '#A9A9A9',
  }

  // Check if it's in our mapping
  if (colorMap[lowerColor]) {
    return colorMap[lowerColor]
  }

  // Check if it's already a valid CSS color
  if (isValidCSSColor(lowerColor)) {
    return lowerColor
  }

  // Default fallback
  return '#CCCCCC'
}
