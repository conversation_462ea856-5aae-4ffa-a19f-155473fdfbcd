// Utility functions for formatting data
// Provides consistent formatting across the application

import { DateTime } from 'luxon'

/**
 * Format currency values
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount)
}

/**
 * Format numbers with thousand separators
 */
export const formatNumber = (
  value: number,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale).format(value)
}

/**
 * Format percentage values
 */
export const formatPercentage = (
  value: number,
  decimals: number = 1,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100)
}

/**
 * Format dates using Luxon
 */
export const formatDate = (
  date: string | Date,
  format: string = 'MMM dd, yyyy'
): string => {
  const dt = typeof date === 'string' ? DateTime.fromISO(date) : DateTime.fromJSDate(date)
  return dt.toFormat(format)
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date: string | Date): string => {
  const dt = typeof date === 'string' ? DateTime.fromISO(date) : DateTime.fromJSDate(date)
  return dt.toRelative() || 'Unknown'
}

/**
 * Format date and time
 */
export const formatDateTime = (
  date: string | Date,
  format: string = 'MMM dd, yyyy HH:mm'
): string => {
  const dt = typeof date === 'string' ? DateTime.fromISO(date) : DateTime.fromJSDate(date)
  return dt.toFormat(format)
}

/**
 * Format file sizes
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Format phone numbers
 */
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Format as (XXX) XXX-XXXX for US numbers
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
  }
  
  // Return original if not a standard format
  return phone
}

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

/**
 * Format order status for display
 */
export const formatOrderStatus = (status: string): string => {
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

/**
 * Format product SKU
 */
export const formatSKU = (sku: string): string => {
  return sku.toUpperCase()
}

/**
 * Format address for display
 */
export const formatAddress = (address: {
  address_line_1: string
  address_line_2?: string
  city_or_village: string
  postal_code: string
  country: string
}): string => {
  const parts = [
    address.address_line_1,
    address.address_line_2,
    address.city_or_village,
    address.postal_code,
    address.country,
  ].filter(Boolean)
  
  return parts.join(', ')
}

/**
 * Format full name
 */
export const formatFullName = (firstName: string, lastName: string): string => {
  return [firstName, lastName].filter(Boolean).join(' ')
}

/**
 * Format initials from name
 */
export const formatInitials = (firstName: string, lastName?: string): string => {
  const first = firstName?.charAt(0)?.toUpperCase() || ''
  const last = lastName?.charAt(0)?.toUpperCase() || ''
  return first + last
}

/**
 * Format duration in minutes to human readable
 */
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}m`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  if (remainingMinutes === 0) {
    return `${hours}h`
  }
  
  return `${hours}h ${remainingMinutes}m`
}

/**
 * Format stock status
 */
export const formatStockStatus = (quantity: number): {
  status: 'in-stock' | 'low-stock' | 'out-of-stock'
  label: string
  color: string
} => {
  if (quantity === 0) {
    return {
      status: 'out-of-stock',
      label: 'Out of Stock',
      color: 'error'
    }
  }
  
  if (quantity < 10) {
    return {
      status: 'low-stock',
      label: 'Low Stock',
      color: 'warning'
    }
  }
  
  return {
    status: 'in-stock',
    label: 'In Stock',
    color: 'success'
  }
}
