// Base types
export interface BaseEntity {
  id: number;
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  results: T[];
  count: number;
  next: string | null;
  previous: string | null;
  page_size: number;
  current_page: number;
  total_pages: number;
}

// User and Authentication types
export interface User extends BaseEntity {
  email: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  is_active: boolean;
  date_joined: string;
}

export interface LoginCredentials {
  username: string; // Changed from email to username to support email or phone
  password: string;
}

export interface RegisterData {
  username: string; // Changed from email to username to support email or phone
  password?: string;
  confirm_password?: string;
  first_name?: string;
  last_name?: string;
  phone_number?: string;
}

export interface AuthResponse {
  user: User;
  access: string; // Changed from access_token to match React-TS
  refresh: string; // Changed from refresh_token to match React-TS
}

// Product types
export interface Category extends BaseEntity {
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parent?: number;
  is_active: boolean;
}

export interface Brand extends BaseEntity {
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  is_active: boolean;
}

export interface ProductType extends BaseEntity {
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
}

export interface ProductAttribute extends BaseEntity {
  name: string;
  slug: string;
  attribute_type: 'text' | 'number' | 'boolean' | 'choice';
  is_required: boolean;
  is_variant_attribute: boolean;
}

export interface ProductAttributeValue extends BaseEntity {
  attribute: ProductAttribute;
  value: string;
  sort_order: number;
}

export interface ProductImage extends BaseEntity {
  image: string;
  alt_text?: string;
  is_primary: boolean;
  sort_order: number;
}

export interface ProductVariant extends BaseEntity {
  sku: string;
  price: string;
  compare_at_price?: string;
  cost_price?: string;
  inventory_quantity: number;
  weight?: string;
  is_active: boolean;
  attributes: ProductAttributeValue[];
  images: ProductImage[];
}

export interface Product extends BaseEntity {
  name: string;
  slug: string;
  description: string;
  short_description?: string;
  category: Category;
  brand?: Brand;
  product_type: ProductType;
  is_active: boolean;
  is_featured: boolean;
  meta_title?: string;
  meta_description?: string;
  variants: ProductVariant[];
  images: ProductImage[];
  attributes: ProductAttributeValue[];
  average_rating?: number;
  review_count: number;
  min_price: string;
  max_price: string;
}

// Cart types
export interface CartItem {
  id: string;
  product: Product;
  variant: ProductVariant;
  quantity: number;
  price: string;
  total: string;
}

export interface Cart {
  items: CartItem[];
  total_items: number;
  subtotal: string;
  tax: string;
  shipping: string;
  total: string;
}

// Address types
export interface Address extends BaseEntity {
  first_name: string;
  last_name: string;
  company?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone_number?: string;
  is_default: boolean;
  address_type: 'billing' | 'shipping';
}

// Order types
export interface OrderItem {
  id: number;
  product: Product;
  variant: ProductVariant;
  quantity: number;
  unit_price: string;
  total_price: string;
}

export interface Order extends BaseEntity {
  order_number: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  items: OrderItem[];
  billing_address: Address;
  shipping_address: Address;
  subtotal: string;
  tax: string;
  shipping_cost: string;
  total: string;
  notes?: string;
  tracking_number?: string;
  shipped_at?: string;
  delivered_at?: string;
}

// Payment types
export interface PaymentMethod {
  id: string;
  type: 'stripe' | 'paypal';
  name: string;
  is_active: boolean;
}

export interface PaymentIntent {
  id: string;
  client_secret: string;
  amount: number;
  currency: string;
  status: string;
}

// Review types
export interface Review extends BaseEntity {
  user: User;
  product: Product;
  rating: number;
  title: string;
  content: string;
  is_verified_purchase: boolean;
  is_approved: boolean;
  helpful_count: number;
}

// Wishlist types
export interface WishlistItem extends BaseEntity {
  user: User;
  product: Product;
  variant?: ProductVariant;
}

// Filter types
export interface ProductFilters {
  category?: string;
  brand?: string[];
  product_type?: string;
  min_price?: number;
  max_price?: number;
  attributes?: Record<string, string[]>;
  search?: string;
  sort?: 'name' | '-name' | 'price' | '-price' | 'created_at' | '-created_at';
  page?: number;
  page_size?: number;
}

// UI State types
export interface UIState {
  isLoading: boolean;
  error: string | null;
  success: string | null;
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'textarea' | 'select' | 'checkbox';
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
}

// Error types
export interface ApiError {
  message: string;
  status: number;
  errors?: Record<string, string[]>;
}

// SEO types
export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  structuredData?: Record<string, any>;
}
