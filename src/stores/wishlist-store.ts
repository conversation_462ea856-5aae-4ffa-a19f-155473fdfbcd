// Wishlist store using Zustand
// Manages user's wishlist items with persistence

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { Product, WishlistItem } from '../types';

interface WishlistState {
  items: WishlistItem[];
  isLoading: boolean;
  error: string | null;
}

interface WishlistActions {
  // Item management
  addItem: (product: Product) => void;
  removeItem: (productId: string) => void;
  clearWishlist: () => void;
  isInWishlist: (productId: string) => boolean;
  
  // State management
  setItems: (items: WishlistItem[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Computed getters
  totalItems: () => number;
  getItem: (productId: string) => WishlistItem | undefined;
}

type WishlistStore = WishlistState & WishlistActions;

const initialState: WishlistState = {
  items: [],
  isLoading: false,
  error: null,
};

export const useWishlistStore = create<WishlistStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,

        // Actions
        addItem: (product) => {
          const { items } = get();
          
          // Check if item already exists
          const existingItem = items.find(item => item.product.id === product.id);
          if (existingItem) {
            return; // Item already in wishlist
          }

          const newItem: WishlistItem = {
            id: `wishlist-${product.id}-${Date.now()}`,
            product,
            added_at: new Date().toISOString(),
          };

          set({ 
            items: [...items, newItem],
            error: null 
          });
        },

        removeItem: (productId) => {
          const { items } = get();
          set({ 
            items: items.filter(item => item.product.id !== productId),
            error: null 
          });
        },

        clearWishlist: () => {
          set({ 
            items: [],
            error: null 
          });
        },

        isInWishlist: (productId) => {
          const { items } = get();
          return items.some(item => item.product.id === productId);
        },

        setItems: (items) => {
          set({ items, error: null });
        },

        setLoading: (loading) => {
          set({ isLoading: loading });
        },

        setError: (error) => {
          set({ error });
        },

        clearError: () => {
          set({ error: null });
        },

        // Computed getters
        totalItems: () => {
          return get().items.length;
        },

        getItem: (productId) => {
          const { items } = get();
          return items.find(item => item.product.id === productId);
        },
      }),
      {
        name: 'wishlist-store',
        getServerSnapshot: () => initialState,
        partialize: (state) => ({
          items: state.items,
        }),
        version: 1,
      }
    ),
    {
      name: 'WishlistStore',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// Selector hooks for better performance
export const useWishlistItems = () => useWishlistStore((state) => state.items);
export const useWishlistLoading = () => useWishlistStore((state) => state.isLoading);
export const useWishlistError = () => useWishlistStore((state) => state.error);
export const useTotalWishlistItems = () => useWishlistStore((state) => state.totalItems());

// Wishlist actions
export const useWishlistActions = () => useWishlistStore((state) => ({
  addItem: state.addItem,
  removeItem: state.removeItem,
  clearWishlist: state.clearWishlist,
  isInWishlist: state.isInWishlist,
  setItems: state.setItems,
  setLoading: state.setLoading,
  setError: state.setError,
  clearError: state.clearError,
  getItem: state.getItem,
}));

// Helper hooks for common wishlist patterns
export const useWishlistState = () => {
  const items = useWishlistItems();
  const isLoading = useWishlistLoading();
  const error = useWishlistError();
  const totalItems = useTotalWishlistItems();

  return {
    items,
    isLoading,
    error,
    totalItems,
    isEmpty: items.length === 0,
  };
};

export const useWishlistItem = (productId: string) => {
  const isInWishlist = useWishlistStore((state) => state.isInWishlist(productId));
  const getItem = useWishlistStore((state) => state.getItem);
  
  return {
    isInWishlist,
    item: getItem(productId),
  };
};
