// UI state management store for global UI components
// Handles navigation, modals, notifications, and theme

import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  timeoutId?: NodeJS.Timeout
}

interface Modal {
  id: string
  component: React.ComponentType<any>
  props?: Record<string, any>
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  onClose?: () => void
}

// Group all action functions in a separate interface
interface UIActions {
  toggleMobileMenu: () => void
  setMobileMenuOpen: (open: boolean) => void
  toggleSearch: () => void
  setSearchOpen: (open: boolean) => void
  toggleCartSidebar: () => void
  setCartSidebarOpen: (open: boolean) => void
  setGlobalLoading: (loading: boolean) => void
  setPageLoading: (loading: boolean) => void
  addNotification: (notification: Omit<Notification, 'id'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
  openModal: (modal: Omit<Modal, 'id'>) => void
  closeModal: (id: string) => void
  closeAllModals: () => void
  setTheme: (theme: 'light' | 'dark') => void
  toggleTheme: () => void
  setSearchQuery: (query: string) => void
  addToSearchHistory: (query: string) => void
  clearSearchHistory: () => void
}

interface UIState {
  // Navigation state
  mobileMenuOpen: boolean
  searchOpen: boolean
  cartSidebarOpen: boolean

  // Loading states
  globalLoading: boolean
  pageLoading: boolean

  // Notifications
  notifications: Notification[]

  // Modals
  modals: Modal[]

  // Theme
  theme: 'light' | 'dark'

  // Search
  searchQuery: string
  searchHistory: string[]

  // Actions are grouped under a single property
  actions: UIActions
}

// Use timestamp + counter for stable ID generation
let notificationCounter = 0
const generateId = () => `${Date.now()}-${++notificationCounter}`

// Initial state for SSR compatibility
const initialState = {
  mobileMenuOpen: false,
  searchOpen: false,
  cartSidebarOpen: false,
  globalLoading: false,
  pageLoading: false,
  notifications: [],
  modals: [],
  theme: 'light' as const,
  searchQuery: '',
  searchHistory: [],
}

export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,

        // Group actions under a single 'actions' property
        // This ensures the 'actions' object has a stable reference
        actions: {
          // Navigation actions
          toggleMobileMenu: () => set((state) => ({
            mobileMenuOpen: !state.mobileMenuOpen
          })),

          setMobileMenuOpen: (open) => set({ mobileMenuOpen: open }),

          toggleSearch: () => set((state) => ({
            searchOpen: !state.searchOpen
          })),

          setSearchOpen: (open) => set({ searchOpen: open }),

          toggleCartSidebar: () => set((state) => ({
            cartSidebarOpen: !state.cartSidebarOpen
          })),

          setCartSidebarOpen: (open) => set({ cartSidebarOpen: open }),

          // Loading actions
          setGlobalLoading: (loading) => set({ globalLoading: loading }),

          setPageLoading: (loading) => set({ pageLoading: loading }),

          // Notification actions
          addNotification: (notification) => {
            const id = generateId()
            const newNotification: Notification = {
              id,
              duration: 5000, // Default 5 seconds
              ...notification,
            }

            // Auto-remove notification after duration
            if (newNotification.duration && newNotification.duration > 0) {
              const timeoutId = setTimeout(() => {
                // Call action via get().actions
                get().actions.removeNotification(id)
              }, newNotification.duration)
              newNotification.timeoutId = timeoutId
            }

            set((state) => ({
              notifications: [...state.notifications, newNotification]
            }))
          },

          removeNotification: (id) => {
            set((state) => {
              // Clear timeout for the notification being removed
              const notification = state.notifications.find(n => n.id === id)
              if (notification?.timeoutId) {
                clearTimeout(notification.timeoutId)
              }

              return {
                notifications: state.notifications.filter(n => n.id !== id)
              }
            })
          },

          clearNotifications: () => {
            set((state) => {
              // Clear all timeouts before clearing notifications
              state.notifications.forEach(notification => {
                if (notification.timeoutId) {
                  clearTimeout(notification.timeoutId)
                }
              })
              return { notifications: [] }
            })
          },

          // Modal actions
          openModal: (modal) => {
            const id = generateId()
            const newModal: Modal = {
              id,
              size: 'md',
              closable: true,
              ...modal,
            }

            set((state) => ({
              modals: [...state.modals, newModal]
            }))
          },

          closeModal: (id) => {
            const modal = get().modals.find(m => m.id === id)
            if (modal?.onClose) {
              modal.onClose()
            }

            set((state) => ({
              modals: state.modals.filter(m => m.id !== id)
            }))
          },

          closeAllModals: () => {
            const { modals } = get()
            modals.forEach(modal => {
              if (modal.onClose) {
                modal.onClose()
              }
            })

            set({ modals: [] })
          },

          // Theme actions
          setTheme: (theme) => set({ theme }),

          toggleTheme: () => set((state) => ({
            theme: state.theme === 'light' ? 'dark' : 'light'
          })),

          // Search actions
          setSearchQuery: (query) => set({ searchQuery: query }),

          addToSearchHistory: (query) => {
            if (!query.trim()) return

            set((state) => {
              const history = state.searchHistory.filter(item => item !== query)
              return {
                searchHistory: [query, ...history].slice(0, 10) // Keep last 10 searches
              }
            })
          },

          clearSearchHistory: () => set({ searchHistory: [] }),
        }
      }),
      {
        name: 'ui-store',
        // The 'actions' property should not be persisted
        partialize: (state) => ({
          theme: state.theme,
          searchHistory: state.searchHistory,
        }),
        version: 1,
      }
    ),
    {
      name: 'UIStore',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
)

// Selector hooks for better performance
export const useMobileMenuOpen = () => useUIStore((state) => state.mobileMenuOpen)
export const useSearchOpen = () => useUIStore((state) => state.searchOpen)
export const useCartSidebarOpen = () => useUIStore((state) => state.cartSidebarOpen)
export const useGlobalLoading = () => useUIStore((state) => state.globalLoading)
export const usePageLoading = () => useUIStore((state) => state.pageLoading)
export const useNotifications = () => useUIStore((state) => state.notifications)
export const useModals = () => useUIStore((state) => state.modals)
export const useTheme = () => useUIStore((state) => state.theme)
export const useSearchQuery = () => useUIStore((state) => state.searchQuery)
export const useSearchHistory = () => useUIStore((state) => state.searchHistory)

// UI actions hook now returns the stable 'actions' object
export const useUIActions = () => useUIStore((state) => state.actions)

// Helper hooks
export const useNotificationCount = () => useUIStore((state) => state.notifications.length)
export const useModalCount = () => useUIStore((state) => state.modals.length)
export const useHasModals = () => useUIStore((state) => state.modals.length > 0)
export const useIsLoading = () => useUIStore((state) => state.globalLoading || state.pageLoading)
