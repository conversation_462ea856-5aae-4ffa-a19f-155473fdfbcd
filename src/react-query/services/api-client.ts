import axios, { AxiosError, AxiosRequestConfig } from "axios"
import { FetchPaginatedResponse } from "../../types/types"

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_URL,
  withCredentials: true, // Include credentials (cookies) with requests
})

class APIClient<TResponse, TRequest = TResponse> {
  endpoint: string

  constructor(endpoint: string) {
    this.endpoint = endpoint
  }

  get = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.get<TResponse>(this.endpoint, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      // Add a return statement to ensure a return value in case of an error
      throw new Error((error as AxiosError).message)
    }
  }

  // This is for paginated responses from the server
  getAll = async (config?: AxiosRequestConfig): Promise<FetchPaginatedResponse<TResponse>> => {
    try {
      const response = await axiosInstance.get<FetchPaginatedResponse<TResponse>>(this.endpoint, config)
      console.log(`${response.config.url}`)
      return response.data
    } catch (error) {
      this.handleError(error)
      // Add a return statement to ensure a return value in case of an error
      throw new Error((error as AxiosError).message)
    }
  }

  post = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.post<TResponse>(this.endpoint, data, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      // Add a return statement to ensure a return value in case of an error
      throw new Error((error as AxiosError).message)
    }
  }

  patch = async (data: Partial<TRequest>, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.patch<TResponse>(this.endpoint, data, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw new Error((error as AxiosError).message)
    }
  }

  delete = async (itemId?: number): Promise<TResponse> => {
    try {
      const response = await axiosInstance.delete(`${this.endpoint}/${itemId}/`)
      return response.data
    } catch (error) {
      this.handleError(error)
      // Add a return statement to ensure a return value in case of an error
      throw new Error((error as AxiosError).message)
    }
  }

  private handleError = (error: unknown): void => {
    if (axios.isAxiosError(error)) {
      // ✅ Don't log cancelled requests as errors
      if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {
        // This is a cancelled request, don't log it as an error
        throw error
      }

      // Don't log expected authentication errors to reduce console noise
      const isAuthError = error.response?.status === 401 || error.response?.status === 403

      if (!isAuthError) {
        console.error("Error message: ", error.message)
        if (error.response) {
          console.error("Response data: ", error.response.data)
          console.error("Response status: ", error.response.status)
        } else if (error.request) {
          console.error("Request data: ", error.request)
        } else {
          console.error("Error config: ", error.config)
        }
      }

      throw {
        message: error.message,
        response: error.response,
      }
    } else {
      console.error("Error: ", error)
    }
    throw error
  }
}

export default APIClient
