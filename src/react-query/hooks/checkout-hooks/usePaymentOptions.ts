import { useQuery } from '@tanstack/react-query'
import APIClient from '../../services/api-client'
import { PaymentOptionsShape } from '../../../types/types'


const usePaymentOptions = () => {
  const apiClient = new APIClient<PaymentOptionsShape[]>('/payments/payment-options/')

  const payOptions = useQuery({
    queryKey: ['payment_options'],
    queryFn: apiClient.get,
    // keepPreviousData: true,
    // refetchOnWindowFocus: false,
    staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
  })

  return payOptions
}

export default usePaymentOptions
