import { useQuery } from '@tanstack/react-query'
import { ProductImageShape } from '../../../types/product-types'
import APIClient from "../../services/api-client"
import { WISHLIST_ITEMS } from '../constants'


interface WishlistShape {
  id: number
  product: {
    id: number
    title: string
    slug: string
    is_active: boolean
    average_rating: number
    product_variant: [
      {
        id: number
        price: number
        product_image: ProductImageShape[]
      }
    ]
  }
  added_at: string
}

const useWishlist = (page: number, enabled: boolean = true) => {

  const apiClient = new APIClient<WishlistShape>(`/wishlist/`)

  return useQuery({
    queryKey: [WISHLIST_ITEMS, page],
    queryFn: () => apiClient.getAll({
      params: {
        page: page
      }
    }),
    enabled, // Only run the query if enabled is true (i.e., user is authenticated)
    // keepPreviousData: true,
    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
    // refetchOnMount: true,
    staleTime: 0,

  })
}

export default useWishlist
