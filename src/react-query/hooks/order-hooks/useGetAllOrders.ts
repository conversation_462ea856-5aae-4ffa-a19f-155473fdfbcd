import { useQuery } from '@tanstack/react-query'
import { OrderShape } from '../../../types/order-types'
import { CACHE_KEY_ORDERS } from '../../hooks/constants'
import APIClient from '../../services/api-client'


const useGetAllOrders = (page: number, queryString: string = '') => {
  const apiClient = new APIClient<OrderShape>(`/orders/`)
  const queryParams = new URLSearchParams(queryString)

  queryParams.set('page', page.toString())

  return useQuery({
    queryKey: [CACHE_KEY_ORDERS, page, queryString],
    queryFn: () => apiClient.getAll({ params: Object.fromEntries(queryParams) }),
    // staleTime: 1000 * 60 * 5, // 5 minutes
  })
}


export default useGetAllOrders
