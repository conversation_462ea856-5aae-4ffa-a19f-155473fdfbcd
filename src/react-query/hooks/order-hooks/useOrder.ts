import { useQuery } from '@tanstack/react-query'
import { OrderShape } from '../../../types/order-types'
import { CACHE_KEY_ORDER_ITEMS } from '../../hooks/constants'
import APIClient from '../../services/api-client'


const useOrder = (orderId: number) => {

  const apiClient = new APIClient<OrderShape>(`/orders/${orderId}`)

  return useQuery({
    // queryKey: [CACHE_KEY_ORDER_ITEMS, orderId],
    queryKey: [CACHE_KEY_ORDER_ITEMS],
    queryFn: () => apiClient.get(),
    staleTime: 0,
  })

}

export default useOrder
