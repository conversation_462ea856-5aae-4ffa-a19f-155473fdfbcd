import { useQuery } from '@tanstack/react-query'
import APIClient from "../../services/api-client"
import { CustomerShape } from '../../../types/store-types'
import { CUSTOMER_DETAILS } from '../constants'


const useCustomerDetails = (enabled: boolean = true) => {

  const apiClient = new APIClient<CustomerShape>('/customers/me/')

  return useQuery({
    queryKey: [CUSTOMER_DETAILS],
    queryFn: () => apiClient.get(),
    enabled,
    // keepPreviousData: true,
    // refetchOnWindowFocus: true,
    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
    // refetchOnMount: true,
  })
}

export default useCustomerDetails
