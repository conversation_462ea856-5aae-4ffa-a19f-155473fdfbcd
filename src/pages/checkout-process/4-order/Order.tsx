import { Link, useParams } from 'react-router-dom'
import moment from 'moment'
import useOrder from '../../../react-query/hooks/order-hooks/useOrder'
import Spinner from '../../../components/utils/spinner/Spinner'
import Alert from '../../../components/utils/alert/Alert'
import Logo from '../../../components/utils/logo/Logo'
import payment_cards from '../../../assets/images/credit_cards.png'
import { OrderedItemShape } from '../../../types/order-types'
import EmptyCart from '../../../components/utils/empty-cart/EmptyCart'
import { loadStripe } from "@stripe/stripe-js"
import styles from './Order.module.scss'
import { Elements } from '@stripe/react-stripe-js'
import CheckoutForm from '../stripe-checkout/CheckoutForm'
import { getErrorMessage } from '../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../types/types'
import LimitTitleLength from '../../../components/utils/TextLimit'
import useClientSecret from '../../../react-query/hooks/checkout-hooks/useClientSecret'
import PayPalCheckout from '../../../components/utils/paypal/PayPalCheckout'
import noImagePlaceholder from '../../../assets/no-image-placeholder.png'


const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY)

const Order = () => {
  const { id } = useParams()
  const orderId = Number(id)
  const { isPending, error, data: order } = useOrder(orderId)
  const { data: clientSecret } = useClientSecret(orderId)

  console.log(id)

  console.log('placed order:', order)

  console.log('clientSecret:', clientSecret)


  return (
    <div>
      {!id ? <EmptyCart message='It seems that you have not placed any orders yet. Please add some products to the cart to checkout!' /> :
        isPending ? <Spinner color='#0091CF' size={20} loading={true} bgOpacity={0} /> :
          error ? <Alert variant="error" message={getErrorMessage(error as AxiosError<ErrorResponse>)} /> :
            !order ? ( // If there  is no order with a given ID, Django with throw a 404 error
              <div className={styles.empty_cart}>
                <p>Your cart is empty. Add some products to the cart to checkout!</p>
                <Link to='/'>Go Shopping</Link>
              </div>
            ) : (
              <>
                <div className='logo_header'>
                  <Logo />
                </div>
                <div className={`container`}>
                  <h3 className={styles.order_summery_title}>Order Summary</h3>
                  <div className={styles.checkout}>
                    <section className={styles.order_summary}>
                      <div className={styles.placed_at}>
                        <h3>Placed at: </h3>
                        <span>{moment(order.placed_at).format('dddd, MMMM D, YYYY, h:mm A')}</span>
                      </div>
                      <div className={styles.contact_info}>
                        <div>
                          <h3>Contact Details: </h3>
                          <p>Deliver to: {order.customer?.first_name} {order.customer?.last_name}</p>
                          <p>Phone: {order.customer?.phone_number}</p>
                          <p>Email to: {order.customer?.email}</p>
                        </div>
                        {/* <hr /> */}
                        <div className={styles.shipping_address}>
                          <h3>Shipping address: </h3>
                          <address>
                            {order.selected_address.full_name},<br />
                            {order.selected_address.street_name},<br />
                            {order.selected_address.postal_code},<br />
                            {order.selected_address.city_or_village}<br />
                          </address>
                        </div >
                      </div>
                      <hr />
                      <div className={styles.cart}>
                        <ul className={styles.cart__item_list}>
                          {order?.ordered_items?.map((item: OrderedItemShape) => (
                            <li key={item.id} className={styles.cart__cart_item}>
                              <div className={styles.cart_items_img}>
                                <img
                                  src={item.product_variant?.product_image?.[0]?.image
                                    ? `${import.meta.env.VITE_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`
                                    : noImagePlaceholder}
                                  alt={item.product.title || "Product image"}
                                />
                              </div>
                              <div className={styles.cart_items_info}>
                                <span>
                                  <Link to={`/products/${item.product.slug}/`}>
                                    <LimitTitleLength title={item.product.title} maxLength={60} />
                                  </Link>
                                </span>
                                <span>(${item.product_variant.price} x {item.quantity})</span>
                                <span>Variant: {item.product_variant?.price_label?.attribute_value}</span>
                                {Object.entries(item.extra_data).map(([key, value], index) => (
                                  <div key={index}>
                                    <p>{key} :</p><p>{value}</p>
                                  </div>
                                ))}
                              </div>
                              <div className={styles.cart_items_quantity}>
                                <p>Qty : {item.quantity}</p>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </div>
                      <hr />
                      <div className={styles.payment_method}>
                        <h3>Payment Method:</h3>
                        <div>
                          {order?.payment_method.id == 2 &&
                            <img src={payment_cards} alt="payment cards" />}
                          <p>{order?.payment_method.name}</p>
                        </div>
                      </div>
                    </section >
                    <section className={styles.pay_order}>
                      <div>
                        <h3>Delivery Status:</h3>
                        <Alert
                          variant={
                            order?.delivery_status === 'Pending' ? 'warning' :
                              order?.delivery_status === 'Processing' ? 'info' :
                                'success'
                          }
                          message={order?.delivery_status}
                        />
                      </div>
                      <div>
                        <h3>Payment Status:</h3>
                        <Alert variant={order?.payment_status === 'Pending' ? 'warning' : 'success'} message={order?.payment_status} />
                      </div>
                      <div className={styles.payment_info}>
                        <div className={styles.total}>
                          <p>Total :</p>
                          <p>${order?.total}</p>
                        </div>
                      </div>
                      <div className={styles.checkout_card}>
                        {order?.payment_status === "Pending" && order?.payment_method?.slug === 'paypal' ? (
                          <div className={styles.paypal_buttons}>
                            <p>Pay with PayPal:</p>
                            {/* <PaypalCheckout totalPrice={order?.total} orderId={order?.id} /> */}

                            {/* <BraintreeCheckout
                              amount={order?.total}
                              orderId={orderId}
                            /> */}

                            {/* <BraintreeWebCheckout
                              amount={order?.total}
                              orderId={orderId}
                            /> */}

                            <PayPalCheckout
                              orderId={order.id}
                              amount={order.total}
                            />
                          </div>
                        ) :
                          order?.payment_status === "Pending" && order?.payment_method?.slug === 'stripe' ? (
                            <div className={styles.payment_options_buttons}>
                              {clientSecret && <Elements
                                // options={{
                                //   mode: 'payment',
                                //   amount: convertToCents(order?.total),
                                //   currency: 'usd',
                                //   // business: "PickyPC",
                                //   // appearance: {}, // Fully customizable with appearance API.
                                // }}

                                // options={{
                                //   clientSecret: order?.payment_intent_id, // Use backend-provided payment_intent_id
                                // }}

                                options={{
                                  clientSecret: clientSecret.client_secret, // Pass the fetched clientSecret here
                                }}

                                stripe={stripePromise}>
                                <CheckoutForm
                                  // clientSecret={clientSecret}
                                  amount={order?.total}
                                  orderId={orderId}
                                // order={order}
                                // customer={{
                                //   email: order.customer.email,
                                //   name: `${order.customer.first_name} ${order.customer.last_name}`,
                                //   // address: {
                                //   //   line1: order.selected_address.address_line_1!,
                                //   //   city: order.selected_address.city_or_village,
                                //   //   postal_code: order.selected_address.postal_code,
                                //   //   country: 'USA'
                                //   // }
                                // }}
                                />

                              </Elements>}
                            </div>
                          ) : (
                            <div className={styles.purchase_done}>
                              {order?.payment_status === "Paid" && <p>Thank you for your purchase.</p>}
                              <Link to='/my-orders'>My orders</Link>
                            </div>
                          )}
                      </div>
                    </section>
                  </div>
                </div>
              </>
            )}
    </div >
  )
}

export default Order