@import '../../../scss/variables';
@import '../../../scss/mixins';


.order_summery_title {
  background-color: $sky-lighter-blue;
  font-weight: bold;
  color: $primary-dark-text-color;
  text-align: center;
  padding: 0.5rem 0;
  font-size: 25px;
}

.checkout {
  display: grid;
  grid-template-columns: 1fr;
}

.order_summary {
  padding: 1rem 1rem 0 1rem;

  h3 {
    font-weight: bold;
    color: $primary-blue;
    font-size: 18px;
  }
}

.placed_at {
  @include flexbox(flex-start, center);
  column-gap: 0.3rem;

  p {
    font-weight: bold;
    color: $primary-blue;
  }
}

.contact_info {
  @include flexbox(space-around, center);
  padding: 1rem 0;

  div {
    h3 {
      font-weight: bold;
      color: $primary-blue;
      font-size: 18px;
    }
  }
}

.cart {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin: 1rem 0;
}

.cart__item_list {
  width: 100%;
}

.cart__cart_item {
  padding: 1rem 0;
  margin: 0;
  width: 100%;
  display: grid;
  grid-template-columns: 20% 60% 20%;
  box-shadow: $box-shadow-1;
}

.cart_items_img {
  @include flexbox(center, center);
  margin: 0 1rem;
  max-width: 100px;

  img {
    object-fit: contain;
  }
}

.cart_items_info {
  @include flexbox(flex-start, flex-start, column);
  gap: 0.5rem;

  span:first-child {
    a {
      color: $primary-dark;
      font-weight: bold;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .cart_item__title {
    font-size: 17.5;
    font-weight: bold;

    &:hover {
      color: $primary-blue;
    }
  }

  .cart_item__extra_data {
    @include flexbox(flex-start, center);
    gap: 0.5rem;
    font-size: 14.5px;
    color: $primary-lighter-text-color;

    p:first-child {
      font-weight: bold;
    }
  }
}

.cart_items_quantity {
  @include flexbox(center, center);
  column-gap: 0.5rem;
  font-weight: bold;
}

.payment_method {
  @include flexbox(center, center, row);
  margin: 1.5rem 0;
  column-gap: 0.5rem;

  div {
    @include flexbox(flex-start, flex-start, column);
    row-gap: 0.5rem;

    >img {
      width: 150px;
    }
  }
}

.payment_options_buttons {
  width: 100%;
}

.pay_order {
  padding: 0 1rem;

  h3 {
    font-weight: bold;
    font-size: 18px;
  }

  >div:nth-child(1),
  >div:nth-child(2) {
    display: grid;
    grid-template-columns: 40% 60%;
    align-items: center;

    p {
      font-weight: bold;
    }

    div {
      width: 100%;
      margin: 0;
    }

    h3 {
      font-weight: bold;
      color: $primary-blue;
    }
  }

  >div:nth-child(1) {
    margin-bottom: 1rem;
  }
}

.payment_info {
  margin: 1rem 0;

  .total {
    display: grid;
    grid-template-columns: 40% 60%;
    align-items: center;
    padding: 1rem 0;

    p:nth-child(1) {
      font-weight: bold;
      color: $primary-blue;
      font-size: 20px;
    }

    p:nth-child(2) {
      font-weight: bold;
      font-size: 20px;
    }
  }
}

.checkout_card {
  border-radius: 20px;
  // background-color: #e9c6c6
}

.paypal_buttons {
  p {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 1rem 0;
  }
}

.purchase_done {
  @include flexbox(center, center, column);
  // align-items: center;
  // background-color: $sky-lighter-blue;
  width: 100%;
  padding: 1rem;

  p {
    font-size: 20px;
    font-weight: bold;
  }

  a {
    padding: .5rem;
    font-size: 18px;
    color: $lighten-blue;
    text-decoration: underline;

    &:hover {
      color: $primary-blue;
    }
  }
}

@media (width > $tablet) {
  .checkout {
    grid-template-columns: 2fr 1fr;
  }

  .order_summary {
    padding: 0 1rem 0 0;
  }

  .placed_at {
    padding: 0;
    margin: 1rem 0;
  }

  .contact_info {
    column-gap: 4rem;
    justify-content: flex-start;
    padding-left: 0;
  }

  .shipping_address {
    margin: 0 0 0 2rem;
  }

  .pay_order {
    margin: 1rem 0 0 0;
    padding: 1.5rem;
    @include flexbox(flex-start, flex-start, column);
    background-color: $sky-lighter-blue;
  }

  .paypal_buttons {
    width: 100%;
    border-radius: 3px;
    background-color: #fff;
    margin: 1rem auto 0 auto;
    padding: 1rem;
  }

  .checkout_card {
    @include flexbox(center, center, column);
    height: 100%;
  }
}