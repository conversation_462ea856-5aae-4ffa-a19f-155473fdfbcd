@import '../../../scss/variables';
@import '../../../scss/mixins';

.place_order {
  background-color: $sky-lighter-blue;
  font-weight: bold;
  color: $primary-dark-text-color;
  text-align: center;
  padding: .5rem 0;
  font-size: 25px;
}

.payment_options_stage {
  display: grid;
  grid-template-columns: 1fr;
}

.logo {
  background-color: $primary-dark;
  padding: 10px 0;
  @include flexbox(center, center);
}

.contact_info {
  @include flexbox(space-around, center);
  padding: 1rem 0;

  div {
    h3 {
      font-weight: bold;
      color: $primary-blue;
      font-size: 18px;
    }
  }
}

.contact_details,
.shipping_address {
  h3 {
    font-weight: bold;
  }
}

.cart {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin: 1rem 0;
}

.cart_items_quantity {
  @include flexbox(center, center);
  column-gap: .5rem;
  font-weight: bold;
}

.payment_options {
  padding: 0 0 0 1rem;
  margin: 1rem auto;

  div {
    margin: 0 0 0 2rem;
    @include flexbox(flex-start, center);
    column-gap: .4rem;

    input {
      margin: .5rem 0;
    }
  }

  h3 {
    font-weight: bold;
    margin: 10px 0;
    color: $primary-blue;
    font-size: 18px;
  }
}

.price_summary {
  background-color: $sky-lighter-blue;
  // padding: 0 1.5rem;
  @include flexbox(flex-start, flex-start, column);
  gap: 1.5rem;

  .prices {
    // @include flexJustify(space-around);
    @include flexbox(flex-start, flex-start, column);
    font-size: $font-size-3;
    row-gap: 1rem;

    div {
      @include flexbox(space-between, center);
    }

    div:last-child {
      p:last-child {
        font-size: $font-size-4;
      }
    }


    p:first-child {
      font-weight: bold;
      color: $primary-dark-text-color;
    }

    p:last-child {
      background-color: #e0adad;
      font-weight: bold;
      color: $primary-blue;
    }
  }

  button {
    @include btn(#fff, $lighten-blue);
    padding: 1rem 0rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: .7px;
    transition: all .3s ease;
    margin: 0 1.5rem;

    &:hover {
      background-color: darken($lighten-blue, 10%);
      color: darken(#fff, 15%);
    }
  }
}

@media (width > $tablet) {
  .payment_options_stage {
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    margin: 1rem 0 0 0;
  }

  .contact_info {
    column-gap: 4rem;
    @include flexbox(flex-start, flex-start);
    padding: 0 0 1rem 0;
  }
}