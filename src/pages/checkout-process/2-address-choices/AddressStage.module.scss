@import '../../../scss/variables';
@import '../../../scss/mixins';

.missing_addresses {
  margin: 0 auto;
  max-width: 600px;
  padding: $padding-5;
}

.address_stage {
  padding: 0 .5rem;

  >h3:first-child {
    background-color: $sky-lighter-blue;
    font-weight: bold;
    color: $primary-dark-text-color;
    text-align: center;
    padding: .5rem 0;
    font-size: 25px;
  }
}

.contact_details {
  margin: 10px 0;


  h3 {
    font-weight: bold;
    color: $primary-blue;
    font-size: 18px;
  }
}

.cart {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin: 1rem 0;
}

.address_selection {
  margin: 1rem 0;

  address {
    margin: 0 0 0 2rem;
    @include flexbox(flex-start, center, row);
    column-gap: .4rem;

    input {
      margin: .5rem 0;
    }
  }

  h3 {
    font-weight: bold;
    margin: 10px 0;
    color: $primary-blue;
    font-size: 18px;
  }

  button {
    @include btn(#fff, $lighten-blue);
    margin: 1rem auto;
    padding: .5rem 1.5rem;
    font-weight: bold;
    text-transform: uppercase;
    border: 2px solid $lighten-blue;
    letter-spacing: .7px;
    transition: all 0.3s ease;

    // &:hover {
    //   background-color: #fff;
    //   border: 2px solid $primary-blue;
    //   color: $primary-blue;
    //   font-weight: bold;
    // }
    &:hover {
      background-color: darken($lighten-blue, 10%);
      border: 2px solid darken($lighten-blue, 10%);
      color: darken(#fff, 15%);
    }
  }
}

@media (width > $tablet) {
  .cart {
    grid-template-columns: 2fr 1fr;
  }
}

@include mobile {
  // define what you wanna do below 430px (mobile)
  // ex: flex-direction: column;
}