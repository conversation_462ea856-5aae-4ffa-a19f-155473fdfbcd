@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  @include flex-center;
  min-height: 100vh;
  padding: $spacing-4;
  background-color: $gray-50;
}

.card {
  max-width: 500px;
  width: 100%;
}

.content {
  @include flex-column-center;
  text-align: center;
  gap: $spacing-6;
}

.icon {
  @include flex-center;
  width: 80px;
  height: 80px;
  background-color: $red-100;
  border-radius: 50%;
  color: $red-600;
  
  svg {
    width: 40px;
    height: 40px;
  }
}

.title {
  margin: 0;
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

.message {
  margin: 0;
  font-size: $font-size-base;
  color: $gray-600;
  line-height: 1.6;
  max-width: 400px;
}

.actions {
  @include flex-center;
  gap: $spacing-3;
  flex-wrap: wrap;
}

.homeButton,
.backButton {
  @include flex-center;
  gap: $spacing-2;
  min-width: 140px;
}

@include mobile-only {
  .actions {
    flex-direction: column;
    width: 100%;
  }
  
  .homeButton,
  .backButton {
    width: 100%;
    justify-content: center;
  }
}
