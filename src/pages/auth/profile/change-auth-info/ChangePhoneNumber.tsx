import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import Logo from '../../../../components/utils/logo/Logo'
import loading_blue from '../../../../assets/loading_svg_blue.svg'
import Alert from '../../../../components/utils/alert/Alert'
import { getErrorMessage } from '../../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../../types/types'
import { changeAuthInfoSchema } from '../../../../types/schemas'
import { useNavigate } from 'react-router-dom'
import useChangeAuthInfo from '../../../../react-query/hooks/auth-hooks/change-auth-info/useChangeAuthInfo'
import styles from './ChangeAuthInfo.module.scss'
import PhoneNumberInput from '../../../../components/utils/phone-number-input/PhoneNumberInput'
import Modal from '../../../../components/utils/modal/Modal'
import useCustomerDetails from '../../../../react-query/hooks/customer-related-hooks/useCustomerDetails'
import authStore from "../../../../zustand/authStore"

export type NewAuthInfoShape = z.infer<typeof changeAuthInfoSchema>

const ChangePhoneNumber = () => {
  const { isLoggedIn } = authStore()
  const { data: customerData } = useCustomerDetails(isLoggedIn)
  const [phoneValue, setPhoneValue] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState<NewAuthInfoShape | null>(null)
  const navigate = useNavigate()
  const { mutation } = useChangeAuthInfo()

  const { register, handleSubmit, formState: { errors }, setValue } = useForm<NewAuthInfoShape>({
    resolver: zodResolver(changeAuthInfoSchema),
    defaultValues: {
      phone_number: customerData?.phone_number || ''
    }
  })

  useEffect(() => {
    if (customerData?.phone_number) {
      const initialPhoneNumber = customerData.phone_number.replace('+', '')
      setPhoneValue(initialPhoneNumber)
      setValue('phone_number', customerData.phone_number)
    }
  }, [customerData, setValue])

  const submitForm = (data: NewAuthInfoShape) => {
    mutation.mutate(data, {
      onSuccess: () => {
        navigate('/user/submit-auth-info-verify-code')
      }
    })
  }

  const onSubmit: SubmitHandler<NewAuthInfoShape> = (data) => {
    // Check if there's an existing phone number and if it's different from the new one
    if (customerData?.phone_number && customerData.phone_number !== data.phone_number) {
      setFormData(data) // Store form data temporarily
      setShowModal(true) // Show modal only when changing existing number
    } else {
      // If no existing phone number or no change, submit directly
      submitForm(data)
    }
  }

  const handleConfirm = () => {
    if (formData) {
      submitForm(formData)
    }
    setShowModal(false)
  }

  const handleCancel = () => {
    setShowModal(false)
  }

  const handleInputChange = (value: string) => {
    setPhoneValue(value)
    setValue("phone_number", `+${value}`)
  }

  return (
    <div className={styles.register_container}>
      <div className={styles.form_container}>
        <div className='logo_header'>
          <Logo />
        </div>
        <h2 className='title'>Change phone number</h2>
        {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <div className='form_group'>
            <PhoneNumberInput
              {...register("phone_number")}
              value={phoneValue}
              defaultCountry="lk"
              onlyCountries={['lk', 'us']}
              onChange={handleInputChange}
              placeholder='Enter phone number'
              pending={mutation.isPending}
            />
            {errors.phone_number && <p>{errors.phone_number.message} 😟</p>}
          </div>
          <section className='btn_container'>
            <button
              type="button"
              className='empty_btn'
              disabled={mutation.isPending}
              onClick={() => navigate('/customer')}>Cancel
            </button>
            <button type="submit" className='empty_btn' disabled={mutation.isPending}>
              {mutation.isPending ? (
                <img src={loading_blue} alt="Loading..." className='loading_svg' />
              ) : (
                'Update'
              )}
            </button>
          </section>
        </form>

        <Modal
          title="Confirm Phone Number Change"
          message="Are you sure you want to change your phone number?"
          show={showModal}
          onClose={handleCancel}
          onConfirm={handleConfirm}
          btn1="Yes"
          btn2="No"
        />
      </div>
    </div>
  )
}

export default ChangePhoneNumber