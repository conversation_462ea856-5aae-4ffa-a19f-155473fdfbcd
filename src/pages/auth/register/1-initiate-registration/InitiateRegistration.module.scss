@import '../../../../scss/variables';
@import '../../../../scss/mixins';
@import '../../../../scss/animations.scss';

.register_container {
  background-image: url(../../../../assets/images/pc_hardware.jpg);
  background-color: #70829e;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100vh;
  position: relative;
  overflow: hidden; // Add this to prevent the blur from extending outside

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: inherit;
    background-size: cover;
    background-position: center;
    filter: blur(10px);
    z-index: 0; // Change this to 0
  }

  // Add this new rule
  >* {
    position: relative;
    z-index: 1;
  }
}

.form_container {
  width: 400px;
  box-shadow: #0000000d 0px 0px 0px 1px, #d1d5db 0px 0px 0px 1px inset;
  padding: $padding-5;
  background-color: #fff;
  border-radius: $border-radius-1;
  // @include slideInAnimation(0.8s, ease-out);
  @include slideInAnimation();

  button {
    margin: 1.5rem auto 1rem auto;
    @include btn(#fff, $primary-blue);
    width: 100%;
    padding: 8px 0;
    transition: all 0.3s ease-in;

    &:hover {
      background-color: darken($primary-blue, 10%);
    }
  }
}

// Main form group container
.form_group {
  @include flexbox(flex-start, stretch, column);
  width: 100%;
  // margin-bottom: 15px;
  // row-gap: 4px;

  // Nested form group (for the email input wrapper)
  .form_group {
    margin-bottom: 0;
    width: 100%;
  }

  .form_label {
    font-weight: bold;
    color: $primary-dark-blue;
    width: 100%;
    display: block;
  }

  .form_input {
    width: 100%;
    border: .1px solid $primary-dark-text-color;
    border-radius: 3px;
    padding: 8px 10px;
    font-size: 16.5px;
    box-sizing: border-box;

    &:focus {
      outline: 2px solid $lighten-blue;
      border: none;
    }
  }

  .form_error {
    color: $error-red;
    text-align: left;
    margin-top: 4px;
    font-size: 14px;
  }
}

.login_or_register {
  text-align: center;

  a {
    color: $primary-blue;
    transition: all 0.3s ease-in;

    &:hover {
      color: darken($primary-blue, 10%);
      text-decoration: underline;
    }
  }
}

@media (width > $tablet) {
  .register_container {
    padding-top: 4rem;
  }

  .form_container {
    padding: 25px;
  }
}