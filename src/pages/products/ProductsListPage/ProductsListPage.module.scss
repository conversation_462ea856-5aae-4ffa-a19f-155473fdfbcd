@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;
  
  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.titleSection {
  flex: 1;
}

.title {
  margin: 0 0 $spacing-2 0;
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
}

.subtitle {
  margin: 0;
  color: $gray-600;
  font-size: $font-size-base;
}

.actions {
  @include flex-start;
  gap: $spacing-3;
}

.createButton {
  @include flex-center;
  gap: $spacing-2;
}

.filtersCard {
  margin-bottom: $spacing-4;
}

.filters {
  @include flex-between;
  gap: $spacing-4;
  
  @include mobile-only {
    flex-direction: column;
    align-items: stretch;
  }
}

.searchSection {
  flex: 1;
  max-width: 400px;
  
  @include mobile-only {
    max-width: none;
  }
}

.searchInput {
  width: 100%;
}

.filterSection {
  @include flex-start;
  gap: $spacing-2;
}

.tableCard {
  padding: 0;
}

// Table cell styles
.productCell {
  @include flex-start;
  gap: $spacing-3;
}

.productInfo {
  flex: 1;
}

.productName {
  margin: 0 0 $spacing-1 0;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-900;
}

.productSku {
  margin: 0;
  font-size: $font-size-xs;
  color: $gray-500;
}

.variantCount {
  font-size: $font-size-sm;
  color: $gray-600;
}

.actions {
  @include flex-start;
  gap: $spacing-1;
}

.error {
  padding: $spacing-6;
  text-align: center;
  
  h2 {
    margin: 0 0 $spacing-3 0;
    color: $red-600;
  }
  
  p {
    margin: 0 0 $spacing-4 0;
    color: $gray-600;
  }
}

// Responsive adjustments
@include mobile-only {
  .container {
    padding: $spacing-4;
  }
  
  .title {
    font-size: $font-size-xl;
  }
  
  .actions {
    width: 100%;
    justify-content: stretch;
  }
  
  .createButton {
    flex: 1;
    justify-content: center;
  }
}
