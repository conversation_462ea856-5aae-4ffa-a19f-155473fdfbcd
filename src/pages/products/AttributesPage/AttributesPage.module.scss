@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.titleSection {
  .title {
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
    margin: 0 0 $spacing-1 0;
  }

  .subtitle {
    color: $gray-600;
    font-size: $font-size-sm;
    margin: 0;
  }
}

.actions {
  @include flex-start;
  gap: $spacing-3;
}

.createButton {
  @include flex-center;
  gap: $spacing-2;
}

.attributesCard {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
}

.cardHeader {
  @include flex-between;
  align-items: center;
  gap: $spacing-4;

  h2 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0;
  }

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.searchContainer {
  @include flex-start;
  gap: $spacing-3;
}

.searchBox {
  position: relative;
  min-width: 300px;

  @include mobile-only {
    min-width: 100%;
  }
}

.searchIcon {
  position: absolute;
  left: $spacing-3;
  top: 50%;
  transform: translateY(-50%);
  color: $gray-400;
  font-size: $font-size-sm;
}

.searchInput {
  padding-left: $spacing-10;
}

.nameCell {
  @include flex-start;
  gap: $spacing-3;
}

.nameIcon {
  @include flex-center;
  width: 32px;
  height: 32px;
  background: $primary-50;
  color: $primary-600;
  border-radius: $border-radius;
  font-size: $font-size-sm;
}

.name {
  font-weight: $font-weight-medium;
  color: $gray-900;
}

.actions {
  @include flex-start;
  gap: $spacing-1;
}

.deleteButton {
  color: $error-600;

  &:hover {
    background: $error-50;
    color: $error-700;
  }
}

// Modal form styles
.form {
  @include flex-column;
  gap: $spacing-4;
}

.formGroup {
  @include flex-column;
  gap: $spacing-2;
}

.label {
  font-weight: $font-weight-medium;
  color: $gray-700;
  font-size: $font-size-sm;
}

.formActions {
  @include flex-end;
  gap: $spacing-3;
  margin-top: $spacing-2;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;
}
