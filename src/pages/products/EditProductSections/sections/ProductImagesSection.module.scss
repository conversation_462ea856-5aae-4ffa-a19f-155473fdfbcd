@use '../../../../scss/variables.scss' as *;
@use '../../../../scss/mixins.scss' as *;


.header {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-md;

  div {
    h2 {
      @include heading-md;
      margin: 0 0 $spacing-xs 0;
      color: $text-primary;
    }

    p {
      @include text-sm;
      margin: 0;
      color: $text-secondary;
    }
  }
}

.emptyState {
  @include flex-center;
  flex-direction: column;
  gap: $spacing-lg;
  padding: $spacing-xl;
  text-align: center;
  color: $text-secondary;

  svg {
    color: $text-placeholder;
  }

  h3 {
    @include heading-sm;
    margin: 0;
    color: $text-primary;
  }

  p {
    @include text-base;
    margin: 0;
  }
}

.variantSelector {
  margin-bottom: $spacing-xl;

  label {
    @include text-sm;
    font-weight: 600;
    color: $text-primary;
    display: block;
    margin-bottom: $spacing-md;
  }
}

.variantTabs {
  display: flex;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.variantTab {
  @include button-reset;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  background-color: $background-primary;
  transition: all 0.2s ease;

  &:hover {
    border-color: $primary-color;
    background-color: $primary-light;
  }

  &.active {
    border-color: $primary-color;
    background-color: $primary-light;

    .variantSku {
      color: $primary-color;
      font-weight: 600;
    }
  }
}

.variantSku {
  @include text-sm;
  color: $text-primary;
  font-weight: 500;
}

.imagesSection {
  border-top: 1px solid $border-color;
  padding-top: $spacing-lg;
}

.sectionHeader {
  @include flex-between;
  align-items: center;
  margin-bottom: $spacing-lg;

  h3 {
    @include heading-sm;
    margin: 0;
    color: $text-primary;
  }
}

.imageCount {
  @include text-sm;
  color: $text-secondary;
  font-weight: 500;
}

.loadingState {
  @include flex-center;
  padding: $spacing-xl;
}

.emptyImages {
  @include flex-center;
  flex-direction: column;
  gap: $spacing-lg;
  padding: $spacing-xl;
  text-align: center;
  color: $text-secondary;

  svg {
    color: $text-placeholder;
  }

  h4 {
    @include heading-sm;
    margin: 0;
    color: $text-primary;
  }

  p {
    @include text-base;
    margin: 0;
  }
}

.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(125px, 1fr)); // Reduced by half
  gap: $spacing-md;
}

.imageCard {
  border: 1px solid $border-color;
  border-radius: $border-radius;
  overflow: hidden;
  background-color: $background-primary;
  cursor: move;
  transition: $transition-all;

  &:hover {
    border-color: $primary-light;
    box-shadow: $shadow-md;
  }

  &.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }

  &.dropTarget {
    border-color: $primary-color;
    background-color: $primary-25;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

.imagePreview {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  background-color: $background-secondary;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
  max-height: 125px; // Reduced by half
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  @include flex-center;
  gap: $spacing-sm;
  opacity: 0;
  transition: opacity 0.2s ease;

  .imagePreview:hover & {
    opacity: 1;
  }
}

.dragIndicatorOverlay {
  position: absolute;
  top: $spacing-xs;
  left: $spacing-xs;
  z-index: $z-5;
  opacity: 0;
  transition: opacity 0.2s ease;

  .imageCard:hover & {
    opacity: 1;
  }
}

.overlayButton {
  background-color: rgba(255, 255, 255, 0.9);
  color: $text-primary;
  border: none;

  &:hover {
    background-color: white;
  }

  &.deleteButton {
    color: $error-color;

    &:hover {
      background-color: rgba($error-color, 0.1);
      color: $error-color;
    }
  }
}

.imageInfo {
  padding: $spacing-md;
}

.altText {
  @include text-sm;
  color: $text-primary;
  margin: 0 0 $spacing-xs 0;
  font-weight: 500;
}

.imageOrder {
  @include text-xs;
  color: $text-secondary;
}

.editForm {
  padding: $spacing-md;

  .formGroup {
    margin-top: $spacing-md;

    label {
      @include text-sm;
      font-weight: 500;
      color: $text-primary;
      display: block;
      margin-bottom: $spacing-sm;
    }
  }
}

.formActions {
  @include flex-end;
  gap: $spacing-sm;
  margin-top: $spacing-lg;
  padding-top: $spacing-md;
  border-top: 1px solid $border-color;
}

.modalForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.dropzoneContainer {
  margin-bottom: $spacing-lg;
}

.dropzone {
  border: 2px dashed $border-color;
  border-radius: $border-radius;
  padding: $spacing-xl;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover,
  &.dragActive {
    border-color: $primary-color;
    background-color: $primary-light;
  }
}

.dropzoneContent {
  @include flex-center;
  flex-direction: column;
  gap: $spacing-md;
  color: $text-secondary;

  svg {
    color: $text-placeholder;
  }

  h4 {
    @include heading-sm;
    margin: 0;
    color: $text-primary;
  }

  p {
    @include text-sm;
    margin: 0;
  }
}

.filePreview {
  @include flex-center;
  flex-direction: column;
  gap: $spacing-md;

  p {
    @include text-sm;
    color: $text-primary;
    margin: 0;
    font-weight: 500;
  }
}

.previewImage {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: $border-radius;
  border: 1px solid $border-color;
}

.formGroup {
  label {
    @include text-sm;
    font-weight: 500;
    color: $text-primary;
    display: block;
    margin-bottom: $spacing-sm;
  }
}

.modalActions {
  @include flex-end;
  gap: $spacing-md;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-color;
}

.deleteModal {
  text-align: center;
}

.deleteImagePreview {
  margin-bottom: $spacing-lg;
  @include flex-center;
}

.deleteImage {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: $border-radius;
  border: 1px solid $border-color;
}

// Image Preview Modal Styles
.previewModal {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.previewImageContainer {
  @include flex-center;
  background-color: $background-secondary;
  border-radius: $border-radius;
  padding: $spacing-lg;
}

.previewImageLarge {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
  border-radius: $border-radius;
  box-shadow: $shadow-lg;
}

.previewInfo {
  h4 {
    @include heading-sm;
    margin: 0 0 $spacing-md 0;
    color: $text-primary;
  }

  p {
    @include text-sm;
    margin: 0 0 $spacing-sm 0;
    color: $text-secondary;

    strong {
      color: $text-primary;
      font-weight: 600;
    }
  }
}

// Drag and Drop Styles
.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: $z-10;
}

.dropTarget {
  border-color: $primary-color !important;
  background-color: $primary-25 !important;
  box-shadow: 0 0 0 2px rgba($primary-color, 0.2) !important;
}

// Responsive design
@media (max-width: 768px) {
  .imageGrid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); // Even smaller on mobile
    gap: $spacing-sm;
  }

  .variantTabs {
    flex-direction: column;
  }

  .variantTab {
    justify-content: space-between;
  }

  .previewImageLarge {
    max-height: 300px;
  }
}