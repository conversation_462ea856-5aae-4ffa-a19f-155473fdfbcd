// Product Types management page
// Allows viewing, creating, editing, and deleting product types

import React, { useState, useMemo } from 'react'
import { FiPlus, FiEdit, FiTrash2, FiSearch, FiSettings } from 'react-icons/fi'
import {
  useProductTypes,
  useCreateProductType,
  useUpdateProductType,
  useDeleteProductType
} from '../../../hooks/products-hooks/use-product-types'
import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { Modal } from '../../../components/ui/Modal'
import { DataTable } from '../../../components/ui/DataTable'
import { Badge } from '../../../components/ui/Badge'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import type { ProductType } from '../../../types/api-types'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import styles from './ProductTypesPage.module.scss'

const productTypeSchema = z.object({
  title: z.string().min(1, 'Product type name is required'),
})

type ProductTypeFormData = z.infer<typeof productTypeSchema>

export const ProductTypesPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingProductType, setEditingProductType] = useState<ProductType | null>(null)
  const [deletingProductType, setDeletingProductType] = useState<ProductType | null>(null)
  const { data: productTypes, isLoading, refetch } = useProductTypes()
  const createProductTypeMutation = useCreateProductType()
  const updateProductTypeMutation = useUpdateProductType()
  const deleteProductTypeMutation = useDeleteProductType()

  // Get mutation pending states
  const isFormPending = createProductTypeMutation.isPending || updateProductTypeMutation.isPending

  const {
    register,
    handleSubmit,
    reset: resetForm,
    formState: { errors },
  } = useForm<ProductTypeFormData>({
    resolver: zodResolver(productTypeSchema),
  })

  // Filter product types based on search term
  const filteredProductTypes = useMemo(() => {
    if (!productTypes) return []

    return productTypes.filter(productType =>
      productType.title.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [productTypes, searchTerm])

  const handleCreateProductType = async (data: ProductTypeFormData) => {
    try {
      if (editingProductType) {
        await updateProductTypeMutation.mutateAsync({
          id: editingProductType.id,
          data: { title: data.title }
        })
      } else {
        await createProductTypeMutation.mutateAsync({ title: data.title })
      }
      setIsCreateModalOpen(false)
      setEditingProductType(null)
      resetForm()
      refetch()
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error('Error saving product type:', error)
    }
  }

  const handleEditProductType = (productType: ProductType) => {
    setEditingProductType(productType)
    resetForm({
      title: productType.title,
    })
  }

  const handleDeleteProductType = async () => {
    if (!deletingProductType) return
    await deleteProductTypeMutation.mutateAsync(deletingProductType.id)
    setDeletingProductType(null)
    refetch()
  }

  const columns = [
    {
      key: 'title',
      header: 'Product Type',
      render: (_: unknown, productType: ProductType) => (
        <div className={styles.nameCell}>
          <span className={styles.nameIcon}><FiSettings /></span>
          <span className={styles.name}>{productType.title}</span>
        </div>
      ),
    },
    {
      key: 'attributes_count',
      header: 'Attributes',
      render: (_: unknown, productType: ProductType) => (
        <Badge variant="secondary">{productType.attributes_count || 0} attributes</Badge>
      ),
    },
    {
      key: 'products_count',
      header: 'Products',
      render: (_: unknown, productType: ProductType) => (
        <Badge variant="info">{productType.products_count || 0} products</Badge>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: unknown, productType: ProductType) => (
        <div className={styles.actions}>
          <PermissionGuard permission="staff.change_producttype">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEditProductType(productType)}
              title="Edit product type"
            >
              <FiEdit />
            </Button>
          </PermissionGuard>
          <PermissionGuard permission="staff.delete_producttype">
            <Button
              variant="ghost"
              size="sm"
              className={styles.deleteButton}
              onClick={() => setDeletingProductType(productType)}
              title="Delete product type"
              disabled={(productType.products_count || 0) > 0}
            >
              <FiTrash2 />
            </Button>
          </PermissionGuard>
        </div>
      ),
      width: '120px',
    },
  ]

  if (isLoading) {
    return <PageLoading message="Loading product types..." />
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Product Types</h1>
          <p className={styles.subtitle}>
            Manage product types and their attribute associations
          </p>
        </div>
        <div className={styles.actions}>
          <PermissionGuard permission="staff.add_producttype">
            <Button
              variant="primary"
              onClick={() => {
                setIsCreateModalOpen(true)
                setEditingProductType(null)
                resetForm({ title: '' })
              }}
              className={styles.createButton}
            >
              <FiPlus />
              Add Product Type
            </Button>
          </PermissionGuard>
        </div>
      </div>

      <Card className={styles.productTypesCard}>
        <CardHeader>
          <div className={styles.cardHeader}>
            <h2>All Product Types</h2>
            <div className={styles.searchContainer}>
              <div className={styles.searchBox}>
                <FiSearch className={styles.searchIcon} />
                <Input
                  type="text"
                  placeholder="Search product types..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={styles.searchInput}
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          <DataTable
            data={filteredProductTypes}
            columns={columns}
            emptyMessage="No product types found. Add your first product type!"
          />
        </CardBody>
      </Card>

      {/* Create/Edit Product Type Modal */}
      <Modal
        isOpen={isCreateModalOpen || !!editingProductType}
        onClose={() => {
          setIsCreateModalOpen(false)
          setEditingProductType(null)
          resetForm()
        }}
        title={editingProductType ? 'Edit Product Type' : 'Create Product Type'}
        size="lg"
      >
        <form onSubmit={handleSubmit(handleCreateProductType)} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="title" className={styles.label}>
              Product Type Name:
            </label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Enter product type title"
              error={errors.title?.message}
              disabled={isFormPending}
            />
            <span className='form__help_text'>E.g, hard-drive (make it singular and lowercase)</span>
          </div>
          <div className={styles.formActions}>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsCreateModalOpen(false)
                setEditingProductType(null)
                resetForm()
              }}
              disabled={isFormPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              className={`${styles.submitButton} ${styles.primaryButton}`}
              disabled={isFormPending}
            >
              {isFormPending ? (
                editingProductType ? 'Updating Product Type...' : 'Creating Product Type...'
              ) : (
                editingProductType ? 'Update Product Type' : 'Create Product Type'
              )}
            </Button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingProductType}
        onClose={() => setDeletingProductType(null)}
        title="Delete Product Type"
        size="sm"
      >
        <div>
          <p>
            Are you sure you want to delete the product type "{deletingProductType?.title}"?
            This action cannot be undone.
          </p>
          <div className={styles.formActions}>
            <Button
              variant="outline"
              onClick={() => setDeletingProductType(null)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleDeleteProductType}
              loading={deleteProductTypeMutation.isPending}
              disabled={deleteProductTypeMutation.isPending}
            >
              {deleteProductTypeMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
