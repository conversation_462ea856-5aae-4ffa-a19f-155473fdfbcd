@import '../../../scss/variables';
@import '../../../scss/mixins';

.breadcrumbs {
  margin: 1rem 0;
  font-size: 0.9rem;
  color: $primary-dark-text-color;
  // padding: 0 0.5rem;

  a,
  span {
    margin: 0 0.25rem;
  }

  a:hover,
  span:hover {
    color: $primary-blue;
    cursor: pointer;
  }

  @media (max-width: $tablet) {
    // padding: 0 0.75rem;
  }
}

.product_details {
  margin: 0rem auto 0 auto;
  max-width: 1200px;
  display: grid;
  grid-template-columns: 55% 45%;


  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    // padding: 0 0.5rem;
  }
}

.product_description {
  padding: 0 .5rem;
  // margin-top: 2rem;

  h3 {
    color: $primary-dark-text-color;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 1rem;
  }

  p {
    line-height: 1.6;
    color: $primary-dark-text-color;
  }
}

.specifications {
  padding: 0 .5rem;
  margin-top: 2rem;

  h3 {
    color: $primary-dark-text-color;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 1rem;
  }
}

.reviews {
  padding: 0 .5rem;
  margin-top: 2rem;

  h3 {
    color: $primary-dark-text-color;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 1rem;
  }
}

.product_tabs {
  margin: 2rem auto;
  max-width: 1200px;

  .tabs_header {
    @include flexbox(flex-start, flex-end);
    border-bottom: 1px solid #ddd;

    button {
      padding: 0.75rem 1.5rem;
      background: none;
      border: none;
      border-bottom: 3px solid transparent;
      font-weight: 500;
      color: $primary-dark-text-color;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        color: $primary-blue;
      }

      &.active {
        color: $primary-blue;
        border-bottom: 3px solid $primary-blue;
      }
    }
  }

  .tabs_content {
    padding: 1.5rem 0;
  }
}


@media (width > 768px) {
  .product_details {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}