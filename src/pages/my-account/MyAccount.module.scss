@import '../../scss/variables';
@import '../../scss/mixins';


.my_account {
  display: grid;
  grid-template-columns: 1fr;

  .navbar {
    // background-color: $sky-lighter-blue;

    h3 {
      padding: 0.5rem 0;
      font-size: 23px;
      text-align: center;
      font-weight: bold;
    }

    .navbar__links {
      width: 100%;
      @include flexbox(center, center); // Center all links horizontally

      .navbar__links__links {
        @include flexbox(center, center, column); // Center links in a vertical column

        a {
          @include flexbox(flex-start, center, row); // Align icon and text
          column-gap: 10px; // Space between icon and text
          padding: 0.9rem 1rem;
          width: 100%; // Full width for consistent layout
          color: $primary-blue;
          font-weight: bold;
          font-size: 17px;
          letter-spacing: 0.4px;

          &:hover {
            text-decoration: underline;
          }

          i {
            font-size: 20px; // Slightly larger icon for clarity
          }
        }
      }
    }
  }

  // .my_account__content {
  //   // Add styles here when needed
  // }
}

@media (min-width: $tablet) {
  .my_account {
    grid-template-columns: 25% 75%;

    .navbar__links {
      @include flexbox(flex-start, stretch); // Align links to the left on larger screens
    }
  }
}