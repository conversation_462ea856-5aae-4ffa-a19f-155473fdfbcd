// Analytics page component
// Displays comprehensive analytics and reporting dashboard

import React, { useState } from 'react'
import { FiTrendingUp, FiUsers, FiShoppingCart, FiPackage, FiDollarSign, FiCalendar } from 'react-icons/fi'
import { useAnalytics } from '../../../hooks/use-analytics'
import { Card } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Badge } from '../../../components/ui/Badge'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { formatCurrency, formatNumber, formatPercentage } from '../../../utils/format'
import styles from './AnalyticsPage.module.scss'

type TimePeriod = 'today' | 'week' | 'month' | 'quarter' | 'year'

export const AnalyticsPage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('month')

  const {
    data: analyticsData,
    isLoading,
    error,
    refetch,
  } = useAnalytics(selectedPeriod)

  const periodOptions = [
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'quarter', label: 'This Quarter' },
    { value: 'year', label: 'This Year' },
  ] as const

  if (isLoading && !analyticsData) {
    return <PageLoading message="Loading analytics..." />
  }

  if (error) {
    return (
      <div className={styles.error}>
        <Card padding="lg">
          <h2>Error Loading Analytics</h2>
          <p>Failed to load analytics data. Please try again.</p>
          <Button onClick={() => refetch()} variant="primary">
            Retry
          </Button>
        </Card>
      </div>
    )
  }

  const stats = analyticsData?.stats || {}
  const trends = analyticsData?.trends || {}

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Analytics</h1>
          <p className={styles.subtitle}>
            Business insights and performance metrics
          </p>
        </div>

        <div className={styles.periodSelector}>
          {periodOptions.map((option) => (
            <Button
              key={option.value}
              variant={selectedPeriod === option.value ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setSelectedPeriod(option.value)}
            >
              {option.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className={styles.metricsGrid}>
        <Card className={styles.metricCard}>
          <div className={styles.metricContent}>
            <div className={styles.metricIcon}>
              <FiDollarSign />
            </div>
            <div className={styles.metricInfo}>
              <h3 className={styles.metricValue}>
                {formatCurrency(stats.revenue || 0)}
              </h3>
              <p className={styles.metricLabel}>Revenue</p>
              {trends.revenue && (
                <div className={styles.metricTrend}>
                  <Badge
                    variant={trends.revenue > 0 ? 'success' : 'error'}
                    size="sm"
                  >
                    <FiTrendingUp />
                    {formatPercentage(Math.abs(trends.revenue))}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </Card>

        <Card className={styles.metricCard}>
          <div className={styles.metricContent}>
            <div className={styles.metricIcon}>
              <FiShoppingCart />
            </div>
            <div className={styles.metricInfo}>
              <h3 className={styles.metricValue}>
                {formatNumber(stats.orders || 0)}
              </h3>
              <p className={styles.metricLabel}>Orders</p>
              {trends.orders && (
                <div className={styles.metricTrend}>
                  <Badge
                    variant={trends.orders > 0 ? 'success' : 'error'}
                    size="sm"
                  >
                    <FiTrendingUp />
                    {formatPercentage(Math.abs(trends.orders))}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </Card>

        <Card className={styles.metricCard}>
          <div className={styles.metricContent}>
            <div className={styles.metricIcon}>
              <FiUsers />
            </div>
            <div className={styles.metricInfo}>
              <h3 className={styles.metricValue}>
                {formatNumber(stats.customers || 0)}
              </h3>
              <p className={styles.metricLabel}>Customers</p>
              {trends.customers && (
                <div className={styles.metricTrend}>
                  <Badge
                    variant={trends.customers > 0 ? 'success' : 'error'}
                    size="sm"
                  >
                    <FiTrendingUp />
                    {formatPercentage(Math.abs(trends.customers))}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </Card>

        <Card className={styles.metricCard}>
          <div className={styles.metricContent}>
            <div className={styles.metricIcon}>
              <FiPackage />
            </div>
            <div className={styles.metricInfo}>
              <h3 className={styles.metricValue}>
                {formatNumber(stats.products || 0)}
              </h3>
              <p className={styles.metricLabel}>Products</p>
              {trends.products && (
                <div className={styles.metricTrend}>
                  <Badge
                    variant={trends.products > 0 ? 'success' : 'error'}
                    size="sm"
                  >
                    <FiTrendingUp />
                    {formatPercentage(Math.abs(trends.products))}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>

      {/* Charts and Detailed Analytics */}
      <div className={styles.chartsGrid}>
        <Card className={styles.chartCard}>
          <Card.Header>
            <h3>Sales Overview</h3>
          </Card.Header>
          <Card.Body>
            <div className={styles.chartPlaceholder}>
              <p>Sales chart will be implemented here</p>
              <p>Revenue: {formatCurrency(stats.revenue || 0)}</p>
              <p>Orders: {formatNumber(stats.orders || 0)}</p>
            </div>
          </Card.Body>
        </Card>

        <Card className={styles.chartCard}>
          <Card.Header>
            <h3>Top Products</h3>
          </Card.Header>
          <Card.Body>
            <div className={styles.chartPlaceholder}>
              <p>Top products chart will be implemented here</p>
              <p>Product performance metrics</p>
            </div>
          </Card.Body>
        </Card>

        <Card className={styles.chartCard}>
          <Card.Header>
            <h3>Customer Growth</h3>
          </Card.Header>
          <Card.Body>
            <div className={styles.chartPlaceholder}>
              <p>Customer growth chart will be implemented here</p>
              <p>New customers: {formatNumber(stats.newCustomers || 0)}</p>
            </div>
          </Card.Body>
        </Card>

        <Card className={styles.chartCard}>
          <Card.Header>
            <h3>Order Status Distribution</h3>
          </Card.Header>
          <Card.Body>
            <div className={styles.chartPlaceholder}>
              <p>Order status distribution chart will be implemented here</p>
              <p>Pending: {formatNumber(stats.pendingOrders || 0)}</p>
              <p>Processing: {formatNumber(stats.processingOrders || 0)}</p>
              <p>Completed: {formatNumber(stats.completedOrders || 0)}</p>
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Additional Analytics Sections */}
      <div className={styles.additionalSections}>
        <Card className={styles.summaryCard}>
          <Card.Header>
            <h3>Performance Summary</h3>
          </Card.Header>
          <Card.Body>
            <div className={styles.summaryGrid}>
              <div className={styles.summaryItem}>
                <span className={styles.summaryLabel}>Average Order Value</span>
                <span className={styles.summaryValue}>
                  {formatCurrency(stats.averageOrderValue || 0)}
                </span>
              </div>
              <div className={styles.summaryItem}>
                <span className={styles.summaryLabel}>Conversion Rate</span>
                <span className={styles.summaryValue}>
                  {formatPercentage(stats.conversionRate || 0)}
                </span>
              </div>
              <div className={styles.summaryItem}>
                <span className={styles.summaryLabel}>Customer Retention</span>
                <span className={styles.summaryValue}>
                  {formatPercentage(stats.customerRetention || 0)}
                </span>
              </div>
              <div className={styles.summaryItem}>
                <span className={styles.summaryLabel}>Return Rate</span>
                <span className={styles.summaryValue}>
                  {formatPercentage(stats.returnRate || 0)}
                </span>
              </div>
            </div>
          </Card.Body>
        </Card>
      </div>
    </div>
  )
}
