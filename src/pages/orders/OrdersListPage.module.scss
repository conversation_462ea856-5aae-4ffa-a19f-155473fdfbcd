// Orders list page styles
// Professional order management interface

@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.ordersPage {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;

  @include responsive(sm) {
    padding: $spacing-8;
  }
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: $font-size-3xl;
  font-weight: $font-weight-bold;
  color: $gray-900;
  margin: 0 0 $spacing-2 0;
  line-height: $line-height-tight;
}

.subtitle {
  font-size: $font-size-base;
  color: $gray-600;
  margin: 0;
  line-height: $line-height-normal;
}

.headerActions {
  @include flex-start;
  gap: $spacing-3;

  @include mobile-only {
    width: 100%;

    button {
      flex: 1;
    }
  }
}

.filters {
  @include flex-start;
  gap: $spacing-4;
  margin-bottom: $spacing-6;
  padding: $spacing-4;
  background: white;
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.searchFilter {
  flex: 1;
  min-width: 300px;

  @include mobile-only {
    width: 100%;
    min-width: auto;
  }
}

.statusFilter {
  min-width: 200px;

  @include mobile-only {
    width: 100%;
    min-width: auto;
  }
}

.statusSelect {
  @include input-base;
  min-width: 100%;
  cursor: pointer;

  &:focus {
    border-color: $primary-500;
    box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
  }
}

.bulkActions {
  @include flex-start;
  gap: $spacing-2;

  @include mobile-only {
    flex-direction: column;
    width: 100%;

    button {
      width: 100%;
    }
  }
}

// Order-specific styles
.orderNumber {
  font-family: $font-family-mono;
  font-weight: $font-weight-semibold;
  color: $primary-600;
}

.customerInfo {
  @include flex-column;
  gap: $spacing-0-5;
}

.customerName {
  font-weight: $font-weight-medium;
  color: $gray-900;
  font-size: $font-size-sm;
}

.customerEmail {
  font-size: $font-size-xs;
  color: $gray-500;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: $spacing-1 $spacing-2;
  border-radius: $border-radius;
  font-size: $font-size-xs;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.05em;

  &.statusPending {
    background-color: $warning-100;
    color: $warning-700;
    border: 1px solid $warning-200;
  }

  &.statusProcessing {
    background-color: $info-100;
    color: $info-700;
    border: 1px solid $info-200;
  }

  &.statusShipped {
    background-color: $primary-100;
    color: $primary-700;
    border: 1px solid $primary-200;
  }

  &.statusDelivered {
    background-color: $success-100;
    color: $success-700;
    border: 1px solid $success-200;
  }

  &.statusCancelled {
    background-color: $error-100;
    color: $error-700;
    border: 1px solid $error-200;
  }
}

// Responsive adjustments
@include responsive(lg) {
  .filters {
    flex-direction: row;
    align-items: center;
  }

  .searchFilter {
    max-width: 400px;
  }

  .statusFilter {
    max-width: 200px;
  }
}

// Loading states
.ordersPage {
  &:has(.loadingContainer) {
    .filters {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// Print styles
@media print {

  .header,
  .filters,
  .bulkActions {
    display: none;
  }

  .ordersPage {
    padding: 0;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .statusBadge {
    border-width: 2px;
    font-weight: $font-weight-bold;
  }

  .orderNumber {
    color: $gray-900;
    text-decoration: underline;
  }
}