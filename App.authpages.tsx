// Preview app for authentication pages
// Shows the fixed Login and Register components

import { useState } from 'react';
import LoginPage from './app/(auth)/login/page';
import RegisterPage from './app/(auth)/register/page';
import { Button } from './src/components/ui/Button';
import './app/globals.scss';

export default function AuthPagesPreview() {
  const [currentPage, setCurrentPage] = useState<'login' | 'register'>('login');

  return (
    <div style={{ minHeight: '100vh' }}>
      {/* Navigation */}
      <div style={{ 
        position: 'fixed', 
        top: 20, 
        right: 20, 
        zIndex: 1000,
        display: 'flex',
        gap: '12px'
      }}>
        <Button
          variant={currentPage === 'login' ? 'primary' : 'secondary'}
          size="sm"
          onClick={() => setCurrentPage('login')}
        >
          Login Page
        </Button>
        <Button
          variant={currentPage === 'register' ? 'primary' : 'secondary'}
          size="sm"
          onClick={() => setCurrentPage('register')}
        >
          Register Page
        </Button>
      </div>

      {/* Page Content */}
      {currentPage === 'login' && <LoginPage />}
      {currentPage === 'register' && <RegisterPage />}
    </div>
  );
}